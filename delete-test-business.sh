#!/bin/bash

# Script to delete the test 'edf' business and related data

DB_URL="postgresql://postgres.vqltspteqqllvhyiupkf:<EMAIL>:6543/postgres"

echo "🗑️ Deleting test business 'edf'..."

# Get the business ID first
BUSINESS_ID=$(psql "$DB_URL" -t -c "SELECT id FROM public.companies WHERE name = 'edf' OR slug = 'edf' LIMIT 1;")

if [ -z "$BUSINESS_ID" ]; then
    echo "⚠️ No business named 'edf' found"
    exit 0
fi

echo "📋 Found business with ID: $BUSINESS_ID"

# Delete related data first (to avoid foreign key constraint issues)
echo "🔄 Deleting related data..."

# Delete dashboard configurations
psql "$DB_URL" -c "DELETE FROM dashboard_configurations WHERE company_id = '$BUSINESS_ID';"

# Delete bot configurations
psql "$DB_URL" -c "DELETE FROM bot_configurations WHERE id IN (SELECT bot_configuration_id FROM companies WHERE id = '$BUSINESS_ID');"

# Delete loyalty members
psql "$DB_URL" -c "DELETE FROM loyalty_members WHERE company_id = '$BUSINESS_ID';"

# Delete points transactions
psql "$DB_URL" -c "DELETE FROM points_transactions WHERE company_id = '$BUSINESS_ID';"

# Delete receipts
psql "$DB_URL" -c "DELETE FROM receipts WHERE company_id = '$BUSINESS_ID';"

# Delete rewards
psql "$DB_URL" -c "DELETE FROM rewards WHERE company_id = '$BUSINESS_ID';"

# Delete business items
psql "$DB_URL" -c "DELETE FROM business_items WHERE company_id = '$BUSINESS_ID';"

# Finally delete the company
echo "🗑️ Deleting the company..."
psql "$DB_URL" -c "DELETE FROM companies WHERE id = '$BUSINESS_ID';"

if [ $? -eq 0 ]; then
    echo "✅ Test business 'edf' deleted successfully"
else
    echo "❌ Failed to delete test business"
    exit 1
fi
