-- Create business_owner_invitations table similar to cashier_invitations
-- This will track business owner invitation links for streamlined onboarding

CREATE TABLE IF NOT EXISTS public.business_owner_invitations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    owner_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(50),
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    temp_password VARCHAR(255) NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    used_at TIMESTAMPTZ NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    telegram_sent_at TIMESTAMPTZ NULL,
    telegram_chat_id VARCHAR(50) NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_business_owner_invitations_company_id ON public.business_owner_invitations(company_id);
CREATE INDEX IF NOT EXISTS idx_business_owner_invitations_token ON public.business_owner_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_business_owner_invitations_email ON public.business_owner_invitations(email);
CREATE INDEX IF NOT EXISTS idx_business_owner_invitations_expires_at ON public.business_owner_invitations(expires_at);

-- Enable RLS
ALTER TABLE public.business_owner_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Super admins can manage all invitations
CREATE POLICY "Super admins can manage all business owner invitations"
    ON public.business_owner_invitations
    FOR ALL
    TO authenticated
    USING (
        auth.jwt()->>'email' = '<EMAIL>' OR
        (auth.jwt()->>'app_metadata')::json->>'is_super_admin' = 'true'
    )
    WITH CHECK (
        auth.jwt()->>'email' = '<EMAIL>' OR
        (auth.jwt()->>'app_metadata')::json->>'is_super_admin' = 'true'
    );

-- RLS Policy: Anyone can read invitation details with valid token (for acceptance)
CREATE POLICY "Anyone can read invitation details with token"
    ON public.business_owner_invitations
    FOR SELECT
    TO authenticated
    USING (true);

-- Grant permissions
GRANT ALL ON public.business_owner_invitations TO authenticated;
GRANT ALL ON public.business_owner_invitations TO service_role;
