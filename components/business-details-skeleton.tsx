import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Building2, Users, CreditCard, Gift } from 'lucide-react'

export function BusinessDetailsSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10" /> {/* Back button */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Building2 className="h-6 w-6 text-muted-foreground" />
              <Skeleton className="h-8 w-48" /> {/* Business name */}
            </div>
            <Skeleton className="h-4 w-64" /> {/* Description */}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Skeleton className="h-6 w-16" /> {/* Active badge */}
          <Skeleton className="h-6 w-20" /> {/* Bot tier badge */}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[Users, CreditCard, Gift].map((Icon, index) => (
          <Card key={index}>
            <CardContent className="flex items-center p-6">
              <Icon className="h-8 w-8 text-muted-foreground mr-3" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-6 w-12" /> {/* Number */}
                <Skeleton className="h-4 w-20" /> {/* Label */}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs Section */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Business Overview</CardTitle>
              <CardDescription>
                <Skeleton className="h-4 w-80" />
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Business Details Grid */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="space-y-2">
                    <Skeleton className="h-4 w-24" /> {/* Label */}
                    <Skeleton className="h-4 w-32" /> {/* Value */}
                  </div>
                ))}
              </div>

              {/* Bot Configuration Section */}
              <div className="mt-6 p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-3">
                  <Skeleton className="h-5 w-5" />
                  <Skeleton className="h-5 w-32" /> {/* Bot Configuration title */}
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="space-y-2">
                      <Skeleton className="h-4 w-20" /> {/* Label */}
                      <Skeleton className="h-4 w-28" /> {/* Value */}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Members Management</CardTitle>
              <CardDescription>
                <Skeleton className="h-4 w-60" />
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-20" />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transactions History</CardTitle>
              <CardDescription>
                <Skeleton className="h-4 w-60" />
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {[...Array(8)].map((_, index) => (
                <div key={index} className="flex items-center justify-between p-3 border-b">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-28" />
                  </div>
                  <div className="text-right space-y-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Business Settings</CardTitle>
              <CardDescription>
                <Skeleton className="h-4 w-64" />
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="space-y-3">
                  <Skeleton className="h-5 w-32" /> {/* Setting label */}
                  <Skeleton className="h-10 w-full max-w-md" /> {/* Input field */}
                </div>
              ))}
              <div className="flex gap-3">
                <Skeleton className="h-10 w-20" /> {/* Save button */}
                <Skeleton className="h-10 w-20" /> {/* Cancel button */}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * Staggered skeleton variant with animation delays
 * Creates a more polished loading experience
 */
export function BusinessDetailsSkeletonStaggered() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header with staggered animation */}
      <div
        className="flex items-center justify-between animate-in fade-in-0 duration-300"
        style={{ animationDelay: '0ms' }}
      >
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10" />
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Building2 className="h-6 w-6 text-muted-foreground" />
              <Skeleton className="h-8 w-48" />
            </div>
            <Skeleton className="h-4 w-64" />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Skeleton className="h-6 w-16" />
          <Skeleton className="h-6 w-20" />
        </div>
      </div>

      {/* Stats Cards with staggered animation */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[Users, CreditCard, Gift].map((Icon, index) => (
          <Card
            key={index}
            className="animate-in fade-in-0 duration-300"
            style={{ animationDelay: `${(index + 1) * 100}ms` }}
          >
            <CardContent className="flex items-center p-6">
              <Icon className="h-8 w-8 text-muted-foreground mr-3" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-6 w-12" />
                <Skeleton className="h-4 w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main content with final stagger */}
      <div
        className="animate-in fade-in-0 duration-300"
        style={{ animationDelay: '400ms' }}
      >
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="members">Members</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Business Overview</CardTitle>
                <CardDescription>
                  <Skeleton className="h-4 w-80" />
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                  {[...Array(6)].map((_, index) => (
                    <div key={index} className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                  ))}
                </div>

                <div className="mt-6 p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <Skeleton className="h-5 w-5" />
                    <Skeleton className="h-5 w-32" />
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {[...Array(3)].map((_, index) => (
                      <div key={index} className="space-y-2">
                        <Skeleton className="h-4 w-20" />
                        <Skeleton className="h-4 w-28" />
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
