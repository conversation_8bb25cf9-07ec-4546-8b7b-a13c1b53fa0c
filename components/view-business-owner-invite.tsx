'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Copy, Eye, ExternalLink, Building2, User, Mail, Phone, Calendar, Clock, CheckCircle2, XCircle, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'

interface ViewBusinessOwnerInviteProps {
  invitationId: string
}

interface InvitationDetails {
  id: string
  email: string
  owner_name: string
  phone_number?: string
  created_at: string
  expires_at: string
  used_at: string | null
  invitation_token: string
  temp_password: string
  companies: {
    name: string
  }
  users: {
    email: string
  }
}

export function ViewBusinessOwnerInvite({ invitationId }: ViewBusinessOwnerInviteProps) {
  const [isOpen, setIsOpen] = useState(false)

  const { data: invitation, isLoading, error } = useQuery({
    queryKey: ['business-owner-invitation', invitationId],
    queryFn: async () => {
      const response = await fetch(`/api/business-owners/invite/${invitationId}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error('Failed to fetch invitation details')
      }

      const result = await response.json()
      return result.invitation as InvitationDetails
    },
    enabled: isOpen && !!invitationId
  })

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast.success(`${label} copied to clipboard`)
  }

  const getStatusInfo = () => {
    if (!invitation) return { color: 'secondary', text: 'Unknown', icon: AlertCircle }
    
    if (invitation.used_at) {
      return { color: 'default', text: 'Accepted', icon: CheckCircle2 }
    }
    
    if (new Date(invitation.expires_at) < new Date()) {
      return { color: 'destructive', text: 'Expired', icon: XCircle }
    }
    
    return { color: 'secondary', text: 'Pending', icon: Clock }
  }

  const statusInfo = getStatusInfo()
  const StatusIcon = statusInfo.icon

  const invitationLink = invitation ? 
    `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/business-owners/accept?token=${invitation.invitation_token}` : 
    ''

  const telegramLink = invitation ?
    `https://t.me/${process.env.TELEGRAM_BOT_USERNAME || 'Loyal_ET_bot'}?start=business_invite_${invitation.invitation_token}` :
    ''

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Business Owner Invitation Details
          </DialogTitle>
          <DialogDescription>
            View and manage business owner invitation information
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-500">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>Failed to load invitation details</p>
          </div>
        ) : invitation ? (
          <div className="space-y-6">
            {/* Status Badge */}
            <div className="flex items-center justify-center">
              <Badge variant={statusInfo.color as "default" | "secondary" | "destructive" | "outline"} className="flex items-center gap-1 px-3 py-1">
                <StatusIcon className="h-4 w-4" />
                {statusInfo.text}
              </Badge>
            </div>

            {/* Owner Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Owner Information
                </h3>
                <div className="space-y-3 pl-6">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Full Name</label>
                    <p className="font-medium">{invitation.owner_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Email Address</label>
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <p className="font-medium">{invitation.email}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(invitation.email, 'Email')}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  {invitation.phone_number && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Phone Number</label>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <p className="font-medium">{invitation.phone_number}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(invitation.phone_number!, 'Phone')}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Business Information
                </h3>
                <div className="space-y-3 pl-6">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Company Name</label>
                    <p className="font-medium">{invitation.companies?.name || 'Unknown Company'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Invited By</label>
                    <p className="font-medium">{invitation.users?.email || 'System Admin'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Timeline Information */}
            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Timeline
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pl-6">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created</label>
                  <p className="font-medium">{new Date(invitation.created_at).toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true })}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Expires</label>
                  <p className="font-medium">{new Date(invitation.expires_at).toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatDistanceToNow(new Date(invitation.expires_at), { addSuffix: true })}
                  </p>
                </div>
                {invitation.used_at && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Accepted</label>
                    <p className="font-medium">{new Date(invitation.used_at).toLocaleString()}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(invitation.used_at), { addSuffix: true })}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Invitation Links */}
            {!invitation.used_at && new Date(invitation.expires_at) > new Date() && (
              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  Invitation Links
                </h3>
                <div className="space-y-3 pl-6">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Web Invitation Link</label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="flex-1 p-2 bg-muted rounded text-sm break-all">
                        {invitationLink}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(invitationLink, 'Invitation link')}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Telegram Bot Link</label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="flex-1 p-2 bg-muted rounded text-sm break-all">
                        {telegramLink}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(telegramLink, 'Telegram link')}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Temporary Password</label>
                    <div className="flex items-center gap-2 mt-1">
                      <code className="flex-1 p-2 bg-muted rounded text-sm font-mono">
                        {invitation.temp_password}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(invitation.temp_password, 'Temporary password')}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Share this password securely with the business owner
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  )
}
