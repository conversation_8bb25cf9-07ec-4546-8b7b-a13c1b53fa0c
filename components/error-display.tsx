import { Alert<PERSON>riangle, RefreshCw, ArrowLeft, Home, AlertCircle, Wifi, WifiOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

interface ErrorDisplayProps {
  error: Error | null
  onRetry?: () => void
  onBack?: () => void
  context?: string
  showTechnicalDetails?: boolean
  className?: string
}

/**
 * Enhanced error display component with contextual actions and better UX
 */
export function ErrorDisplay({
  error,
  onRetry,
  onBack,
  context = 'loading data',
  showTechnicalDetails = false,
  className = ''
}: ErrorDisplayProps) {
  const router = useRouter()

  if (!error) return null

  // Determine error type and appropriate message
  const getErrorInfo = (error: Error) => {
    const message = error.message.toLowerCase()

    if (message.includes('unauthorized') || message.includes('401')) {
      return {
        type: 'auth',
        title: 'Access Denied',
        description: 'You don\'t have permission to access this resource.',
        icon: AlertCircle,
        color: 'destructive' as const,
        suggestions: ['Please sign in again', 'Contact your administrator if this persists']
      }
    }

    if (message.includes('forbidden') || message.includes('403')) {
      return {
        type: 'forbidden',
        title: 'Insufficient Privileges',
        description: 'Super admin privileges are required for this action.',
        icon: AlertCircle,
        color: 'destructive' as const,
        suggestions: ['Contact the system administrator', 'Verify your account permissions']
      }
    }

    if (message.includes('network') || message.includes('fetch')) {
      return {
        type: 'network',
        title: 'Connection Problem',
        description: 'Unable to connect to the server.',
        icon: WifiOff,
        color: 'default' as const,
        suggestions: ['Check your internet connection', 'Try again in a moment']
      }
    }

    if (message.includes('timeout')) {
      return {
        type: 'timeout',
        title: 'Request Timeout',
        description: 'The server took too long to respond.',
        icon: AlertTriangle,
        color: 'default' as const,
        suggestions: ['The server might be busy', 'Try again in a moment']
      }
    }

    if (message.includes('not found') || message.includes('404')) {
      return {
        type: 'notfound',
        title: 'Resource Not Found',
        description: 'The requested resource could not be found.',
        icon: AlertTriangle,
        color: 'default' as const,
        suggestions: ['Check if the resource still exists', 'Navigate back and try again']
      }
    }

    // Generic error
    return {
      type: 'generic',
      title: 'Something went wrong',
      description: `An error occurred while ${context}.`,
      icon: AlertTriangle,
      color: 'destructive' as const,
      suggestions: ['Try refreshing the page', 'Contact support if this persists']
    }
  }

  const errorInfo = getErrorInfo(error)
  const Icon = errorInfo.icon

  const handleRetry = () => {
    if (onRetry) {
      toast.info('Retrying...')
      onRetry()
    }
  }

  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      router.back()
    }
  }

  return (
    <div className={`container mx-auto py-6 max-w-2xl ${className}`}>
      <Card className="border-2">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className={`p-3 rounded-full ${
              errorInfo.color === 'destructive'
                ? 'bg-destructive/10 text-destructive'
                : 'bg-muted text-muted-foreground'
            }`}>
              <Icon className="h-8 w-8" />
            </div>
          </div>
          <CardTitle className="text-xl">{errorInfo.title}</CardTitle>
          <CardDescription className="text-base">
            {errorInfo.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Error Badge */}
          <div className="flex justify-center">
            <Badge variant={errorInfo.color} className="px-3 py-1">
              Error Type: {errorInfo.type.toUpperCase()}
            </Badge>
          </div>

          {/* Suggestions */}
          {errorInfo.suggestions && errorInfo.suggestions.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Suggestions</AlertTitle>
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1 mt-2">
                  {errorInfo.suggestions.map((suggestion, index) => (
                    <li key={index} className="text-sm">{suggestion}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Technical Details (collapsible) */}
          {showTechnicalDetails && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Technical Details</AlertTitle>
              <AlertDescription>
                <code className="text-xs break-all bg-muted p-2 rounded block mt-2">
                  {error.message}
                </code>
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {onRetry && (
              <Button onClick={handleRetry} size="lg" className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
            )}

            <Button
              variant="outline"
              onClick={handleBack}
              size="lg"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Go Back
            </Button>

            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              size="lg"
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Dashboard
            </Button>
          </div>

          {/* Additional Help */}
          <div className="text-center text-sm text-muted-foreground border-t pt-4">
            <p>
              If this problem persists, please{' '}
              <Button variant="link" className="h-auto p-0 text-sm" asChild>
                <a href="mailto:<EMAIL>">contact support</a>
              </Button>{' '}
              with the error details.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Simplified error display for inline use
 */
export function InlineErrorDisplay({
  error,
  onRetry,
  context = 'loading data'
}: Pick<ErrorDisplayProps, 'error' | 'onRetry' | 'context'>) {
  if (!error) return null

  return (
    <Alert variant="destructive" className="my-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription className="flex items-center justify-between">
        <span>Failed to {context}: {error.message}</span>
        {onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="ml-4"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

/**
 * Network-specific error with retry logic
 */
export function NetworkErrorDisplay({ onRetry }: { onRetry?: () => void }) {
  return (
    <div className="text-center py-8">
      <div className="flex justify-center mb-4">
        <div className="p-3 rounded-full bg-muted text-muted-foreground">
          <WifiOff className="h-8 w-8" />
        </div>
      </div>
      <h3 className="text-lg font-semibold mb-2">Connection Lost</h3>
      <p className="text-muted-foreground mb-4">
        Please check your internet connection and try again.
      </p>
      {onRetry && (
        <Button onClick={onRetry} className="flex items-center gap-2 mx-auto">
          <Wifi className="h-4 w-4" />
          Reconnect
        </Button>
      )}
    </div>
  )
}
