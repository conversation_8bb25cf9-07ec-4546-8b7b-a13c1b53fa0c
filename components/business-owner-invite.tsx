'use client'

import { useState } from 'react'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { QRCodeSVG } from 'qrcode.react'
import {
  MessageCircle,
  Send,
  Copy,
  QrCode,
  CheckCircle,
  Loader2,
  Building2,
  Eye,
  EyeOff
} from 'lucide-react'

interface BusinessOwnerInviteProps {
  companyId: string
  companyName: string
  trigger?: React.ReactNode
}

interface InvitationResponse {
  success: boolean
  invitation: {
    id: string
    email: string
    ownerName: string
    phoneNumber?: string
    expiresAt: string
    invitationToken: string
    invitationLink: string
    telegramLink: string
    tempPassword: string
  }
}

export function BusinessOwnerInvite({
  companyId,
  companyName,
  trigger
}: BusinessOwnerInviteProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    ownerName: '',
    phoneNumber: ''
  })
  const [invitationResult, setInvitationResult] = useState<InvitationResponse | null>(null)
  const [copiedLink, setCopiedLink] = useState(false)
  const [copiedPassword, setCopiedPassword] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showQRCode, setShowQRCode] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.email || !formData.ownerName) {
      toast.error('Please fill in all required fields')
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/business-owners/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          ownerName: formData.ownerName,
          phoneNumber: formData.phoneNumber || undefined,
          companyId
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send invitation')
      }

      setInvitationResult(data)
      toast.success('🎉 Business owner invitation created successfully!')

    } catch (error) {
      console.error('Error sending invitation:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to send invitation')
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async (text: string, type: 'link' | 'password') => {
    try {
      await navigator.clipboard.writeText(text)
      if (type === 'link') {
        setCopiedLink(true)
        setTimeout(() => setCopiedLink(false), 2000)
        toast.success('Invitation link copied!')
      } else {
        setCopiedPassword(true)
        setTimeout(() => setCopiedPassword(false), 2000)
        toast.success('Temporary password copied!')
      }
    } catch {
      toast.error('Failed to copy to clipboard')
    }
  }

  const shareViaWhatsApp = () => {
    if (!invitationResult?.invitation.invitationLink) return

    const message = `Hi ${formData.ownerName}! 👋\\n\\nYou've been invited to manage your business account at ${companyName} on the Loyal ET platform!\\n\\nClick this link to get started:\\n${invitationResult.invitation.invitationLink}\\n\\nTemporary Password: ${invitationResult.invitation.tempPassword}\\n\\n📱 You can also join via Telegram: ${invitationResult.invitation.telegramLink}\\n\\nWelcome to Loyal ET! 🎉`

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  const shareViaSMS = () => {
    if (!invitationResult?.invitation.invitationLink) return

    const message = `Hi ${formData.ownerName}! You've been invited to manage ${companyName} on Loyal ET. Link: ${invitationResult.invitation.invitationLink} Password: ${invitationResult.invitation.tempPassword}`
    const smsUrl = `sms:?body=${encodeURIComponent(message)}`
    window.open(smsUrl)
  }

  const resetForm = () => {
    setFormData({ email: '', ownerName: '', phoneNumber: '' })
    setInvitationResult(null)
    setCopiedLink(false)
    setCopiedPassword(false)
    setShowPassword(false)
    setShowQRCode(false)
  }

  const handleClose = () => {
    setIsOpen(false)
    resetForm()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open)
      if (!open) resetForm()
    }}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="gap-2">
            <Building2 className="h-4 w-4" />
            Invite Business Owner
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Invite Business Owner
          </DialogTitle>
          <DialogDescription>
            Create an invitation link for a business owner to manage their account for <strong>{companyName}</strong>
          </DialogDescription>
        </DialogHeader>

        {!invitationResult ? (
          // Invitation Form
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="ownerName">Business Owner Name *</Label>
              <Input
                id="ownerName"
                type="text"
                placeholder="John Doe"
                value={formData.ownerName}
                onChange={(e) => setFormData(prev => ({ ...prev, ownerName: e.target.value }))}
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phoneNumber">Phone Number (optional)</Label>
              <Input
                id="phoneNumber"
                type="tel"
                placeholder="+251 912 345 678"
                value={formData.phoneNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                disabled={isLoading}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Invitation...
                  </>
                ) : (
                  'Create Invitation'
                )}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          // Success State with Sharing Options
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
              <h3 className="text-lg font-semibold text-green-700">
                Invitation Created Successfully!
              </h3>
              <p className="text-sm text-muted-foreground">
                The invitation has been created for <strong>{invitationResult.invitation.ownerName}</strong>
              </p>
            </div>

            {/* Invitation Link */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Invitation Link</CardTitle>
                <CardDescription>Share this link to allow the business owner to create their account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                  <code className="flex-1 text-sm font-mono break-all">
                    {invitationResult.invitation.invitationLink}
                  </code>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => copyToClipboard(invitationResult.invitation.invitationLink, 'link')}
                    className="shrink-0"
                  >
                    {copiedLink ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Temporary Password */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Temporary Password</CardTitle>
                <CardDescription>Share this password with the business owner for initial login</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                  <code className="flex-1 text-sm font-mono">
                    {showPassword ? invitationResult.invitation.tempPassword : '••••••••••••'}
                  </code>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowPassword(!showPassword)}
                    className="shrink-0"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => copyToClipboard(invitationResult.invitation.tempPassword, 'password')}
                    className="shrink-0"
                  >
                    {copiedPassword ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Sharing Options */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Share Invitation</CardTitle>
                <CardDescription>Choose how to send the invitation to the business owner</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <Button onClick={shareViaWhatsApp} className="gap-2">
                    <MessageCircle className="h-4 w-4" />
                    WhatsApp
                  </Button>
                  <Button onClick={shareViaSMS} variant="outline" className="gap-2">
                    <Send className="h-4 w-4" />
                    SMS
                  </Button>
                </div>
                <div className="flex justify-center">
                  <Button
                    onClick={() => setShowQRCode(!showQRCode)}
                    variant="outline"
                    size="sm"
                    className="gap-2"
                  >
                    <QrCode className="h-4 w-4" />
                    {showQRCode ? 'Hide' : 'Show'} QR Code
                  </Button>
                </div>
                {showQRCode && (
                  <div className="flex justify-center p-4 bg-white rounded-lg border">
                    <QRCodeSVG
                      value={invitationResult.invitation.invitationLink}
                      size={200}
                      level="M"
                      includeMargin
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            <DialogFooter>
              <Button onClick={handleClose}>
                Done
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
