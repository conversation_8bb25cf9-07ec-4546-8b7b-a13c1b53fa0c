'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { BusinessOwnerInvite } from '@/components/business-owner-invite'
import { toast } from 'sonner'
import {
  Building2,
  Bot,
  User,
  Activity,
  BarChart3,
  Users,
  Gift,
  CreditCard,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  UserPlus
} from 'lucide-react'

interface BusinessDetailsDialogProps {
  businessId: string
  isOpen: boolean
  onClose: () => void
}

interface BusinessDetails {
  business: {
    id: string
    name: string
    slug: string
    logo_url?: string
    primary_color?: string
    points_expiration_days: number
    bot_tier: 'standard' | 'premium'
    bot_configuration_id?: string
    is_active: boolean
    created_at: string
    points_earning_ratio: number
    administrator_id: string
    business_type?: string
    onboarding_completed?: boolean
    setup_wizard_step: number
    bot_configurations?: {
      id: string
      bot_token: string
      bot_username: string
      webhook_url: string
      webhook_secret: string
      is_active: boolean
      last_activity?: string
      message_count: number
      created_at: string
      creation_status: string
      last_health_check?: string
    }
  }
  owner: {
    administrator_id: string
    role: string
    created_at: string
    users: {
      id: string
      email: string
      raw_user_meta_data: Record<string, unknown>
      created_at: string
      last_sign_in_at?: string
    }
  }
  stats: {
    members: number
    transactions: number
    rewards: number
  }
  recentTransactions: Array<{
    id: string
    transaction_type: string
    points_earned: number
    created_at: string
    loyalty_members: {
      name: string
      loyalty_id: string
    }
  }>
}

export function BusinessDetailsDialog({ businessId, isOpen, onClose }: BusinessDetailsDialogProps) {
  const [details, setDetails] = useState<BusinessDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchBusinessDetails = useCallback(async () => {
    if (!businessId) return

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/admin/businesses/${businessId}`, {
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to fetch business details')
      }

      const result = await response.json()
      setDetails(result.data)
    } catch (error) {
      console.error('Error fetching business details:', error)
      setError(error instanceof Error ? error.message : 'Failed to load business details')
      toast.error('Failed to load business details')
    } finally {
      setLoading(false)
    }
  }, [businessId])

  // Fetch details when dialog opens
  useEffect(() => {
    if (isOpen && businessId) {
      fetchBusinessDetails()
    }
  }, [isOpen, businessId, fetchBusinessDetails])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getBotStatusBadge = () => {
    if (!details?.business) return null

    const { bot_tier, bot_configurations } = details.business

    if (bot_tier === 'standard') {
      return <Badge variant="secondary">Standard Bot</Badge>
    }

    if (bot_configurations) {
      const status = bot_configurations.is_active ? 'Active' : 'Inactive'
      const variant = bot_configurations.is_active ? 'default' : 'destructive'
      return <Badge variant={variant}>Premium Bot ({status})</Badge>
    }

    return <Badge variant="outline">Premium (Not Configured)</Badge>
  }

  const getOnboardingStatus = () => {
    if (!details?.business) return null

    const { onboarding_completed, setup_wizard_step } = details.business

    if (onboarding_completed) {
      return <Badge variant="default"><CheckCircle className="h-3 w-3 mr-1" />Complete</Badge>
    }

    return (
      <Badge variant="outline">
        <AlertCircle className="h-3 w-3 mr-1" />
        Step {setup_wizard_step || 1}
      </Badge>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Business Details
          </DialogTitle>
          <DialogDescription>
            Comprehensive view of business information, settings, and performance
          </DialogDescription>
        </DialogHeader>

        {loading && (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin mr-2" />
            <p>Loading business details...</p>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
            <Button variant="outline" size="sm" className="mt-2" onClick={fetchBusinessDetails}>
              Try Again
            </Button>
          </div>
        )}

        {details && (
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="owner">Owner Info</TabsTrigger>
              <TabsTrigger value="bot">Bot Config</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              {/* Business Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      {details.business.name}
                    </span>
                    <div className="flex items-center gap-2">
                      <Badge variant={details.business.is_active ? "default" : "secondary"}>
                        {details.business.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                      {getOnboardingStatus()}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <p className="font-medium">Business ID</p>
                      <p className="text-muted-foreground font-mono text-xs">{details.business.id}</p>
                    </div>
                    <div>
                      <p className="font-medium">Slug</p>
                      <p className="text-muted-foreground">{details.business.slug}</p>
                    </div>
                    <div>
                      <p className="font-medium">Business Type</p>
                      <p className="text-muted-foreground">{details.business.business_type || 'Not specified'}</p>
                    </div>
                    <div>
                      <p className="font-medium">Points Ratio</p>
                      <p className="text-muted-foreground">{details.business.points_earning_ratio}:1</p>
                    </div>
                    <div>
                      <p className="font-medium">Points Expiry</p>
                      <p className="text-muted-foreground">{details.business.points_expiration_days} days</p>
                    </div>
                    <div>
                      <p className="font-medium">Primary Color</p>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-4 h-4 rounded border"
                          style={{ backgroundColor: details.business.primary_color || '#000000' }}
                        />
                        <p className="text-muted-foreground">{details.business.primary_color || '#000000'}</p>
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Created</p>
                      <p className="text-muted-foreground">{formatDate(details.business.created_at)}</p>
                    </div>
                    <div>
                      <p className="font-medium">Bot Tier</p>
                      <p className="text-muted-foreground">{getBotStatusBadge()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="flex items-center p-6">
                    <Users className="h-8 w-8 text-blue-500 mr-3" />
                    <div>
                      <p className="text-2xl font-bold">{details.stats.members}</p>
                      <p className="text-sm text-muted-foreground">Members</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="flex items-center p-6">
                    <CreditCard className="h-8 w-8 text-green-500 mr-3" />
                    <div>
                      <p className="text-2xl font-bold">{details.stats.transactions}</p>
                      <p className="text-sm text-muted-foreground">Transactions</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="flex items-center p-6">
                    <Gift className="h-8 w-8 text-purple-500 mr-3" />
                    <div>
                      <p className="text-2xl font-bold">{details.stats.rewards}</p>
                      <p className="text-sm text-muted-foreground">Rewards</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="owner" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Owner Information
                    </span>
                    <BusinessOwnerInvite
                      companyId={details.business.id}
                      companyName={details.business.name}
                      trigger={
                        <Button size="sm" className="gap-2">
                          <UserPlus className="h-4 w-4" />
                          Invite Owner
                        </Button>
                      }
                    />
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="font-medium">Name</p>
                      <p className="text-muted-foreground">{String(details.owner.users?.raw_user_meta_data?.name) || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="font-medium">Email</p>
                      <p className="text-muted-foreground">{details.owner.users?.email || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-muted-foreground">{String(details.owner.users?.raw_user_meta_data?.phone) || 'Not provided'}</p>
                    </div>
                    <div>
                      <p className="font-medium">Role</p>
                      <Badge>{details.owner.role}</Badge>
                    </div>
                    <div>
                      <p className="font-medium">Account Created</p>
                      <p className="text-muted-foreground">{details.owner.users?.created_at ? formatDate(details.owner.users.created_at) : 'Unknown'}</p>
                    </div>
                    <div>
                      <p className="font-medium">Last Sign In</p>
                      <p className="text-muted-foreground">
                        {details.owner.users?.last_sign_in_at
                          ? formatDate(details.owner.users.last_sign_in_at)
                          : 'Never'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="bot" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bot className="h-5 w-5" />
                    Bot Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {details.business.bot_configurations ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="font-medium">Bot Username</p>
                          <p className="text-muted-foreground">@{details.business.bot_configurations.bot_username}</p>
                        </div>
                        <div>
                          <p className="font-medium">Bot Status</p>
                          <Badge variant={details.business.bot_configurations.is_active ? "default" : "destructive"}>
                            {details.business.bot_configurations.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <div>
                          <p className="font-medium">Status</p>
                          <Badge variant={details.business.bot_configurations.creation_status === 'complete' ? "default" : "destructive"}>
                            {details.business.bot_configurations.creation_status || 'Unknown'}
                          </Badge>
                        </div>
                        <div>
                          <p className="font-medium">Last Health Check</p>
                          <Badge variant={details.business.bot_configurations.last_health_check ? "default" : "outline"}>
                            {details.business.bot_configurations.last_health_check ? formatDate(details.business.bot_configurations.last_health_check) : 'None'}
                          </Badge>
                        </div>
                        <div>
                          <p className="font-medium">Total Messages</p>
                          <p className="text-muted-foreground">{details.business.bot_configurations.message_count || 0}</p>
                        </div>
                        <div>
                          <p className="font-medium">Last Activity</p>
                          <p className="text-muted-foreground">
                            {details.business.bot_configurations.last_activity
                              ? formatDate(details.business.bot_configurations.last_activity)
                              : 'Never'
                            }
                          </p>
                        </div>
                        <div>
                          <p className="font-medium">Webhook URL</p>
                          <p className="text-muted-foreground text-xs font-mono break-all">
                            {details.business.bot_configurations.webhook_url}
                          </p>
                        </div>
                        <div>
                          <p className="font-medium">Bot Created</p>
                          <p className="text-muted-foreground">{formatDate(details.business.bot_configurations.created_at)}</p>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(`https://t.me/${details.business.bot_configurations!.bot_username}`, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open Bot
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Bot className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">No Bot Configuration</h3>
                      <p className="text-muted-foreground">
                        {details.business.bot_tier === 'premium'
                          ? 'Premium bot tier selected but not configured yet'
                          : 'Business is using the standard shared bot'
                        }
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="activity" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Recent Transactions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {details.recentTransactions.length > 0 ? (
                    <div className="space-y-3">
                      {details.recentTransactions.map((transaction) => (
                        <div key={transaction.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div>
                            <p className="font-medium">{transaction.loyalty_members.name}</p>
                            <p className="text-sm text-muted-foreground">
                              ID: {transaction.loyalty_members.loyalty_id} • {transaction.transaction_type}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-green-600">+{transaction.points_earned} points</p>
                            <p className="text-sm text-muted-foreground">
                              {formatDate(transaction.created_at)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-semibold mb-2">No Recent Activity</h3>
                      <p className="text-muted-foreground">No transactions have been recorded yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
