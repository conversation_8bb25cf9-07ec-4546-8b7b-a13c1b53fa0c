'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'

// UI Components
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Loader2, Trash2, Clock, Mail, AlertCircle, CheckCircle2, XCircle, Building2 } from 'lucide-react'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ViewBusinessOwnerInvite } from '@/components/view-business-owner-invite'

interface BusinessOwnerInvitation {
  id: string
  email: string
  owner_name: string
  phone_number?: string
  created_at: string
  expires_at: string
  used_at: string | null
  companies: {
    name: string
  }
  users: {
    email: string
  }
}


export function BusinessOwnerInvitationManagement() {
  const queryClient = useQueryClient()

  // Query to fetch all business owner invitations (super admin only)
  const { data: invitations, isLoading } = useQuery({
    queryKey: ['business-owner-invitations'],
    queryFn: async () => {
      const response = await fetch('/api/business-owners/invite?listAll=true', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Error fetching business owner invitations:', errorText)
        toast.error('Failed to load business owner invitations')
        return []
      }

      const result = await response.json()
      return result.invitations || []
    }
  })

  // Mutation to cancel a business owner invitation
  const cancelInvitationMutation = useMutation({
    mutationFn: async (invitationId: string) => {
      const response = await fetch(`/api/business-owners/invite?id=${invitationId}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`Error response from server: ${response.status}`, errorText)
        throw new Error(errorText)
      }

      return response.json()
    },
    onSuccess: () => {
      toast.success('Business owner invitation cancelled successfully')
      queryClient.invalidateQueries({ queryKey: ['business-owner-invitations'] })
    },
    onError: (error) => {
      console.error('Error cancelling business owner invitation:', error)
      toast.error('Failed to cancel invitation')
    }
  })

  // Separate invitations into pending and accepted
  const pendingInvitations = invitations?.filter((inv: BusinessOwnerInvitation) => !inv.used_at) || []
  const acceptedInvitations = invitations?.filter((inv: BusinessOwnerInvitation) => inv.used_at) || []

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5 text-muted-foreground" />
          <span>Business Owner Invitations</span>
        </CardTitle>
        <CardDescription>
          Manage business owner invitations sent to potential business owners. Monitor invitation status and manage pending requests.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pending" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="pending" className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              Pending Invitations ({pendingInvitations.length})
            </TabsTrigger>
            <TabsTrigger value="accepted" className="flex items-center gap-1">
              <CheckCircle2 className="h-4 w-4" />
              Accepted Invitations ({acceptedInvitations.length})
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="pending">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : pendingInvitations.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Owner Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Invited</TableHead>
                    <TableHead>Expires</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingInvitations.map((invitation: BusinessOwnerInvitation) => (
                    <TableRow key={invitation.id}>
                      <TableCell className="font-medium">{invitation.owner_name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          {invitation.email}
                        </div>
                      </TableCell>
                      <TableCell>{invitation.companies?.name || 'Unknown'}</TableCell>
                      <TableCell>{formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true })}</TableCell>
                      <TableCell>
                        {new Date(invitation.expires_at) < new Date() ? (
                          <div className="flex items-center gap-1 text-red-500">
                            <AlertCircle className="h-4 w-4" />
                            Expired
                          </div>
                        ) : (
                          formatDistanceToNow(new Date(invitation.expires_at), { addSuffix: true })
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(invitation.expires_at) < new Date() ? (
                          <div className="flex items-center gap-1 text-red-500">
                            <XCircle className="h-4 w-4" />
                            Expired
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-amber-500">
                            <Clock className="h-4 w-4" />
                            Pending
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <ViewBusinessOwnerInvite invitationId={invitation.id} />
                          {/* Only show delete button if not expired */}
                          {new Date(invitation.expires_at) > new Date() && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => cancelInvitationMutation.mutate(invitation.id)}
                              disabled={cancelInvitationMutation.isPending}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Building2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No pending business owner invitations found.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="accepted">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : acceptedInvitations.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Owner Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Invited</TableHead>
                    <TableHead>Accepted</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {acceptedInvitations.map((invitation: BusinessOwnerInvitation) => (
                    <TableRow key={invitation.id}>
                      <TableCell className="font-medium">{invitation.owner_name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          {invitation.email}
                        </div>
                      </TableCell>
                      <TableCell>{invitation.companies?.name || 'Unknown'}</TableCell>
                      <TableCell>{formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true })}</TableCell>
                      <TableCell>{formatDistanceToNow(new Date(invitation.used_at!), { addSuffix: true })}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-green-500">
                          <CheckCircle2 className="h-4 w-4" />
                          Accepted
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <ViewBusinessOwnerInvite invitationId={invitation.id} />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => cancelInvitationMutation.mutate(invitation.id)}
                            disabled={cancelInvitationMutation.isPending}
                            title="Delete accepted invitation to allow resending"
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircle2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No accepted business owner invitations found.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
