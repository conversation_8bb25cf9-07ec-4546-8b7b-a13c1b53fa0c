#!/usr/bin/env node

// Test script to create and test business owner invitation flow
const http = require('http');

const BASE_URL = 'http://localhost:3000';

// Test data
const testData = {
  email: '<EMAIL>',
  ownerName: 'edf',
  phoneNumber: '+***********',
  companyId: '77d7eadb-0eb5-4719-9ac6-6b6d18f5eb16'
};

async function makeRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed, headers: res.headers });
        } catch {
          resolve({ status: res.statusCode, data: body, headers: res.headers });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testBusinessOwnerInvitation() {
  console.log('🧪 Testing Business Owner Invitation Flow\n');

  try {
    // Step 1: Try to create business owner invitation (will fail due to auth)
    console.log('📧 Step 1: Creating business owner invitation...');
    const createResponse = await makeRequest('POST', '/api/business-owners/invite', testData);
    
    if (createResponse.status === 401) {
      console.log('❌ Expected: Authentication required for invitation creation');
      console.log('   This is normal - the API requires proper authentication\n');
    } else {
      console.log('✅ Invitation created:', createResponse.data);
      
      if (createResponse.data.invitation) {
        const { invitationToken, invitationLink } = createResponse.data.invitation;
        console.log('🔗 Invitation Link:', invitationLink);
        console.log('🎫 Token:', invitationToken);
        
        // Step 2: Test invitation validation
        console.log('\n🔍 Step 2: Testing invitation validation...');
        const validateResponse = await makeRequest('GET', `/api/business-owners/accept?token=${invitationToken}`);
        console.log('Validation Response:', validateResponse.data);
        
        // Step 3: Test invitation acceptance (would require password)
        console.log('\n✅ Step 3: Invitation system is working correctly');
        console.log('   - Invitation created successfully');
        console.log('   - Token validation working');
        console.log('   - Ready for user acceptance');
      }
    }

    // Step 4: Test direct invitation validation with a test token
    console.log('\n🔍 Step 4: Testing invitation validation endpoint...');
    const testToken = 'test_token_123';
    const directValidateResponse = await makeRequest('GET', `/api/business-owners/accept?token=${testToken}`);
    
    if (directValidateResponse.status === 404) {
      console.log('✅ Expected: Invalid token returns 404');
      console.log('   Invitation validation endpoint is working correctly\n');
    } else {
      console.log('Unexpected response:', directValidateResponse.data);
    }

    // Step 5: Check current database state
    console.log('📊 Step 5: Current database state summary:');
    console.log('   - User exists: <EMAIL>');
    console.log('   - Company exists: edf');
    console.log('   - Missing: business_owner_invitation record');
    console.log('   - Missing: company_administrators record');
    
    console.log('\n💡 Solution Required:');
    console.log('   1. Create proper business owner invitation via authenticated request');
    console.log('   2. Test complete invitation acceptance flow');
    console.log('   3. Verify authentication works correctly');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testBusinessOwnerInvitation();
