import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// Force dynamic rendering - prevent static caching of this API route
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const { company_id } = await request.json()

    if (!company_id) {
      return NextResponse.json({ error: 'company_id required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Get bot configuration for the company
    const { data: botConfig, error: fetchError } = await supabase
      .from('bot_configurations')
      .select('*')
      .eq('company_id', company_id)
      .single()

    if (fetchError || !botConfig) {
      return NextResponse.json({ error: 'Bot configuration not found' }, { status: 404 })
    }

    // Create new webhook URL with encoded token
    const encodedBotToken = encodeURIComponent(botConfig.bot_token)
    const newWebhookUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/telegram/premium/${encodedBotToken}`

    // Update webhook with Telegram
    const telegramResponse = await fetch(`https://api.telegram.org/bot${botConfig.bot_token}/setWebhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: newWebhookUrl,
        secret_token: botConfig.webhook_secret,
        allowed_updates: ['message', 'callback_query'],
        drop_pending_updates: true,
        max_connections: 40
      })
    })

    const telegramResult = await telegramResponse.json()

    if (!telegramResult.ok) {
      return NextResponse.json({
        error: 'Failed to update Telegram webhook',
        details: telegramResult
      }, { status: 500 })
    }

    // Update database with new webhook URL
    const { error: updateError } = await supabase
      .from('bot_configurations')
      .update({
        webhook_url: newWebhookUrl,
        last_health_check: new Date().toISOString()
      })
      .eq('company_id', company_id)

    if (updateError) {
      console.error('Failed to update database:', updateError)
      return NextResponse.json({
        error: 'Webhook updated with Telegram but failed to update database',
        details: updateError
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Webhook reconfigured successfully',
      old_webhook_url: botConfig.webhook_url,
      new_webhook_url: newWebhookUrl,
      telegram_result: telegramResult
    })

  } catch (error) {
    console.error('Error reconfiguring webhook:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
