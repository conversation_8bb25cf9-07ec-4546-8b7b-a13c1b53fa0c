import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// Force dynamic rendering - prevent static caching
export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const supabase = getServiceRoleClient()
    const { token: rawToken } = await params

    console.log('Debug - Raw token from URL:', rawToken)
    console.log('Debug - Raw token length:', rawToken.length)

    const decodedToken = decodeURIComponent(rawToken)
    console.log('Debug - Decoded token:', decodedToken)
    console.log('Debug - Decoded token length:', decodedToken.length)

    // Search for bot configuration
    const { data: botConfig, error } = await supabase
      .from('bot_configurations')
      .select('id, bot_token, bot_username, company_id')
      .eq('bot_token', decodedToken)
      .single()

    console.log('Debug - Database query result:', { botConfig, error })

    // Also search without decoding to compare
    const { data: botConfig2, error: error2 } = await supabase
      .from('bot_configurations')
      .select('id, bot_token, bot_username, company_id')
      .eq('bot_token', rawToken)
      .single()

    console.log('Debug - Database query result (raw):', { botConfig2, error2 })

    // Return debug info
    return NextResponse.json({
      debug: {
        rawToken,
        rawTokenLength: rawToken.length,
        decodedToken,
        decodedTokenLength: decodedToken.length,
        tokensEqual: rawToken === decodedToken,
        databaseResult: {
          withDecoded: { found: !!botConfig, error: error?.message },
          withRaw: { found: !!botConfig2, error: error2?.message }
        }
      }
    })

  } catch (error) {
    console.error('Debug endpoint error:', error)
    return NextResponse.json({
      error: 'Debug error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
