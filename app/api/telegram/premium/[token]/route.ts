import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { PremiumBotHandler } from '@/lib/handlers/premium-bot-handler'
import { headers } from 'next/headers'

// Force dynamic rendering - prevent static caching of this API route
export const dynamic = 'force-dynamic'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const supabase = getServiceRoleClient()
    const { token } = await params
    
    console.log(`[Premium Webhook] Received request for token: ${token.substring(0, 10)}...`)
    console.log(`[Premium Webhook] Token length: ${token.length}`)    // Get bot configuration by bot token (the token is the bot_token in the URL)
    const { data: botConfig, error: configError } = await supabase
      .from('bot_configurations')
      .select(`
        *,
        companies(
          id,
          name,
          points_earning_ratio,
          administrator_id
        )
      `)
      .eq('bot_token', token)
      .eq('is_active', true)
      .single()

    if (configError || !botConfig) {
      console.error(`[Premium Webhook] Bot configuration not found for token: ${token.substring(0, 10)}...`)
      return NextResponse.json({ error: 'Invalid bot token' }, { status: 404 })
    }

    console.log(`[Premium Webhook] Found bot config for: ${botConfig.bot_username}`)

    // Verify webhook secret if configured
    if (botConfig.webhook_secret) {
      const headersList = await headers()
      const telegramSignature = headersList.get('X-Telegram-Bot-Api-Secret-Token')

      if (!telegramSignature || telegramSignature !== botConfig.webhook_secret) {
        console.error(`[Premium Webhook] Invalid webhook secret for ${botConfig.bot_username}`)
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }

    // Parse the update
    const update = await request.json()

    if (!update) {
      console.error(`[Premium Webhook] No update data received for ${botConfig.bot_username}`)
      return NextResponse.json({ error: 'No update data' }, { status: 400 })
    }

    console.log(`[Premium Webhook] Processing update for ${botConfig.bot_username}:`, {
      updateId: update.update_id,
      messageType: update.message ? 'message' : update.callback_query ? 'callback_query' : 'other',
      chatId: update.message?.chat?.id || update.callback_query?.message?.chat?.id,
      text: update.message?.text?.substring(0, 50) || 'N/A'
    })

    // Create handler instance and process update
    const handler = new PremiumBotHandler(botConfig)
    await handler.handleUpdate(update)

    // Update last activity timestamp
    await supabase
      .from('bot_configurations')
      .update({
        last_activity: new Date().toISOString(),
        message_count: (botConfig.message_count || 0) + 1
      })
      .eq('id', botConfig.id)

    console.log(`[Premium Webhook] Successfully processed update for ${botConfig.bot_username}`)

    return NextResponse.json({ ok: true })

  } catch (error) {
    console.error('[Premium Webhook] Error processing webhook:', error)

    // Log error to database if we have bot config
    try {
      const supabase = getServiceRoleClient()
      const { token } = await params

      const { data: botConfig } = await supabase
        .from('bot_configurations')
        .select('id, bot_username')
        .eq('bot_token', token)
        .single()

      if (botConfig) {
        console.error(`[Premium Webhook] Error for ${botConfig.bot_username}:`, error)
      }
    } catch (logError) {
      console.error('[Premium Webhook] Failed to log error to database:', logError)
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Health check endpoint for premium bots
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const supabase = getServiceRoleClient()
    const { token } = await params

    // Get bot configuration by bot token (the token is the bot_token in the URL)
    const { data: botConfig, error } = await supabase
      .from('bot_configurations')
      .select(`
        id,
        bot_username,
        is_active,
        last_activity,
        last_health_check,
        message_count,
        companies(name)
      `)
      .eq('bot_token', token)
      .single()

    if (error || !botConfig) {
      return NextResponse.json({ error: 'Bot not found' }, { status: 404 })
    }

    // Update health check timestamp
    await supabase
      .from('bot_configurations')
      .update({
        last_health_check: new Date().toISOString()
      })
      .eq('id', botConfig.id)

    return NextResponse.json({
      status: 'healthy',
      bot_username: botConfig.bot_username,
      company_name: 'Unknown',
      is_active: botConfig.is_active,
      last_activity: botConfig.last_activity,
      message_count: botConfig.message_count || 0,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Premium Webhook] Health check error:', error)
    return NextResponse.json(
      { error: 'Health check failed' },
      { status: 500 }
    )
  }
}

// Options for CORS if needed
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Telegram-Bot-Api-Secret-Token',
    },
  })
}
