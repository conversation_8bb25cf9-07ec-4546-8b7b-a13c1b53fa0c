import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST() {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          set(_name: string, _value: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          remove(_name: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes
          },
        },
      }
    )

    // Verify user is authenticated using getUser() for server-side security
    const {
      data: { user },
      error: authError,
    } = await serverSupabase.auth.getUser()

    if (!user || authError) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const userId = user.id

    // Use service role client for database operations
    const supabase = getServiceRoleClient()

    // Get enhanced onboarding status to validate completion
    const { data: onboardingStatus, error: statusError } = await supabase.rpc('get_enhanced_onboarding_status', {
      p_user_id: userId
    })

    if (statusError || !onboardingStatus) {
      console.error('Error checking onboarding status:', statusError)
      return NextResponse.json(
        { error: 'Failed to verify onboarding status' },
        { status: 500 }
      )
    }

    if (!onboardingStatus.hasCompany) {
      return NextResponse.json(
        { error: 'No company found for user' },
        { status: 404 }
      )
    }

    const { company: companyData, progress } = onboardingStatus

    console.log('✅ Completing onboarding for company:', companyData.name)
    console.log('📊 Progress breakdown:', progress)

    // Check if minimum requirements are met (at least 60% overall progress)
    if (progress.overall < 60) {
      return NextResponse.json(
        {
          error: 'Minimum onboarding requirements not met',
          currentProgress: progress.overall,
          minimumRequired: 60,
          nextSteps: onboardingStatus.nextSteps
        },
        { status: 400 }
      )
    }

    // Mark onboarding as complete with completion date
    const { data: updatedCompany, error: updateError } = await supabase
      .from('companies')
      .update({
        onboarding_completed: true,
        onboarding_completion_date: new Date().toISOString()
      })
      .eq('id', companyData.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating onboarding status:', updateError)
      return NextResponse.json(
        { error: 'Failed to update onboarding status' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      company: updatedCompany,
      progress,
      completedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in complete onboarding API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
