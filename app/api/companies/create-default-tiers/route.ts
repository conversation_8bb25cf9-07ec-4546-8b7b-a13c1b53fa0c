import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = getServiceRoleClient() // Use service role

    const { companyId } = await request.json()

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Check which tiers already exist
    const { data: existingTiers, error: checkError } = await supabase
      .from('tier_definitions')
      .select('tier_name')
      .eq('company_id', companyId)

    if (checkError) {
      console.error('Error checking existing tiers:', checkError)
      return NextResponse.json(
        { error: 'Failed to check existing tiers' },
        { status: 500 }
      )
    }
    
    // Get existing tier names
    const existingTierNames = existingTiers?.map(tier => tier.tier_name.toLowerCase()) || [];
    console.log('Existing tier names:', existingTierNames);
    
    // If all standard tiers exist, return early
    const standardTierNames = ['bronze', 'silver', 'gold', 'platinum'];
    const allTiersExist = standardTierNames.every(name => 
      existingTierNames.includes(name.toLowerCase())
    );
    
    if (allTiersExist) {
      return NextResponse.json({
        success: true,
        message: 'All standard tiers already exist',
        existingCount: existingTiers?.length || 0
      });
    }

    // Define all standard tiers
    const allDefaultTiers = [
      {
        company_id: companyId,
        tier_name: 'Bronze',
        minimum_points: 0,
        benefits_description: 'Welcome tier with basic benefits: 1x points earning, standard support'
      },
      {
        company_id: companyId,
        tier_name: 'Silver',
        minimum_points: 250,
        benefits_description: 'Enhanced benefits for loyal customers: 1.2x points earning, priority support, 5% bonus on special offers'
      },
      {
        company_id: companyId,
        tier_name: 'Gold',
        minimum_points: 500,
        benefits_description: 'Premium benefits for top customers: 1.5x points earning, VIP support, 10% bonus on special offers, early access to new products'
      },
      {
        company_id: companyId,
        tier_name: 'Platinum',
        minimum_points: 1000,
        benefits_description: 'Elite benefits for VIP customers: 2x points earning, dedicated concierge service, 15% bonus on all purchases, exclusive event invitations'
      }
    ];
    
    // Filter out tiers that already exist
    const tiersToCreate = allDefaultTiers.filter(tier => 
      !existingTierNames.includes(tier.tier_name.toLowerCase())
    );
    
    console.log(`Creating ${tiersToCreate.length} missing tiers:`, tiersToCreate.map(t => t.tier_name));
    
    // If no tiers need to be created, return
    if (tiersToCreate.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No new tiers needed',
        existingCount: existingTiers?.length || 0
      });
    }

    // Insert only the missing tiers
    const { data: tierDefinitions, error: tiersError } = await supabase
      .from('tier_definitions')
      .insert(tiersToCreate)
      .select()

    if (tiersError) {
      console.error('Error creating tier definitions:', tiersError)
      return NextResponse.json(
        { error: 'Failed to create tier definitions', details: tiersError.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      tiers: tierDefinitions,
      created: tiersToCreate.map(t => t.tier_name),
      message: `Successfully created ${tiersToCreate.length} missing tiers`
    })

  } catch (error) {
    console.error('Error in create-default-tiers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
