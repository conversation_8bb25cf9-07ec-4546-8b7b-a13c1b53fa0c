import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { randomBytes } from 'crypto'

// Test endpoint to create business owner invitation without authentication
// This is for testing purposes only
export async function POST(request: NextRequest) {
  try {
    const serviceClient = getServiceRoleClient()
    const body = await request.json()
    
    const { email, ownerName, phoneNumber, companyId } = body

    // Generate unique invitation token and temporary password
    const invitationToken = randomBytes(16).toString('hex')
    const tempPassword = randomBytes(8).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 12)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 14) // Expires in 14 days

    // Get the administrator ID for invited_by (use the same user for testing)
    const { data: existingAdmin, error: adminError } = await serviceClient
      .from('administrators')
      .select('id')
      .eq('email', email)
      .single()
    
    if (adminError || !existingAdmin) {
      return NextResponse.json(
        { error: 'Administrator not found in system' },
        { status: 404 }
      )
    }

    // Delete any existing invitation for this email/company combination
    await serviceClient
      .from('business_owner_invitations')
      .delete()
      .eq('email', email)
      .eq('company_id', companyId)

    // Create invitation record
    const { data: invitation, error: insertError } = await serviceClient
      .from('business_owner_invitations')
      .insert({
        company_id: companyId,
        invited_by: existingAdmin.id, // Use the administrator ID
        email,
        owner_name: ownerName,
        phone_number: phoneNumber,
        invitation_token: invitationToken,
        temp_password: tempPassword,
        expires_at: expiresAt.toISOString(),
      })
      .select()
      .single()

    if (insertError) {
      console.error('Failed to create business owner invitation:', insertError)
      return NextResponse.json(
        { error: `Failed to create invitation: ${insertError.message}` },
        { status: 500 }
      )
    }

    // Also ensure the user is in company_administrators table
    const { error: companyAdminError } = await serviceClient
      .from('company_administrators')
      .upsert({
        administrator_id: existingAdmin.id,
        company_id: companyId,
        role: 'OWNER'
      }, {
        onConflict: 'administrator_id,company_id'
      })

    if (companyAdminError) {
      console.error('Failed to create company administrator:', companyAdminError)
      // Don't fail the request if this fails
    }

    // Create invitation link
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const invitationLink = `${baseUrl}/business-owners/accept?token=${invitationToken}`

    return NextResponse.json({
      success: true,
      invitation: {
        id: invitation.id,
        email,
        ownerName,
        phoneNumber,
        expiresAt: invitation.expires_at,
        invitationToken,
        invitationLink,
        tempPassword
      },
      message: `Test business owner invitation created for ${ownerName} (${email})`
    })

  } catch (error) {
    console.error('Test invitation creation error:', error)
    return NextResponse.json(
      { error: 'Internal error' },
      { status: 500 }
    )
  }
}
