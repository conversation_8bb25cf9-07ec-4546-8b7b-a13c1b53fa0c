import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getServiceRoleClient } from '@/lib/supabase'

// Helper function to check if user is super admin
async function checkSuperAdmin() {
  const supabase = await createClient()
  const { data: { user }, error } = await supabase.auth.getUser()

  if (error || !user) {
    return { isSuper: false, user: null }
  }

  const isSuperAdmin = user.app_metadata?.is_super_admin === true ||
                      user.email === '<EMAIL>'

  return { isSuper: isSuperAdmin, user }
}

// GET /api/admin/businesses/[id] - Get single business details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Check if user is super admin
    const { isSuper, user } = await checkSuperAdmin()

    if (!isSuper || !user) {
      return NextResponse.json({ error: 'Unauthorized - Super admin access required' }, { status: 401 })
    }

    const supabase = getServiceRoleClient()

    console.log('[Admin Business Details] Super admin fetching business:', id)

    const { data: business, error } = await supabase
      .from('companies')
      .select(`
        id,
        name,
        slug,
        logo_url,
        primary_color,
        points_expiration_days,
        bot_tier,
        bot_configuration_id,
        is_active,
        created_at,
        points_earning_ratio,
        administrator_id,
        business_type,
        onboarding_completed,
        setup_wizard_step,
        bot_configurations:bot_configuration_id (
          id,
          bot_token,
          bot_username,
          webhook_url,
          webhook_secret,
          is_active,
          last_activity,
          message_count,
          created_at,
          creation_status,
          last_health_check
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('[Admin Business Details] Error fetching business:', error)
      return NextResponse.json({ error: 'Business not found' }, { status: 404 })
    }

    // Get business owner information
    const { data: ownerInfo, error: ownerError } = await supabase
      .from('company_administrators')
      .select(`
        administrator_id,
        role,
        created_at
      `)
      .eq('company_id', id)
      .eq('role', 'OWNER')
      .single()

    let ownerUserInfo = null
    if (ownerInfo && !ownerError) {
      // Fetch user data separately to avoid the column error
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select(`
          id,
          email,
          raw_user_meta_data,
          created_at,
          last_sign_in_at
        `)
        .eq('id', ownerInfo.administrator_id)
        .single()

      if (userData && !userError) {
        ownerUserInfo = userData
      } else {
        console.error('[Admin Business Details] Error fetching user data:', userError)
      }
    }

    if (ownerError) {
      console.error('[Admin Business Details] Error fetching owner:', ownerError)
    }

    // Get business stats
    const [
      { count: membersCount },
      { count: transactionsCount },
      { count: rewardsCount }
    ] = await Promise.all([
      supabase.from('loyalty_members').select('*', { count: 'exact', head: true }).eq('company_id', id),
      supabase.from('points_transactions').select('*', { count: 'exact', head: true }).eq('company_id', id),
      supabase.from('rewards').select('*', { count: 'exact', head: true }).eq('company_id', id)
    ])

    // Get recent activities
    const { data: recentTransactions } = await supabase
      .from('points_transactions')
      .select(`
        id,
        transaction_type,
        points_earned,
        created_at,
        loyalty_members (
          name,
          loyalty_id
        )
      `)
      .eq('company_id', id)
      .order('created_at', { ascending: false })
      .limit(5)

    return NextResponse.json({
      success: true,
      data: {
        business,
        owner: {
          ...ownerInfo,
          users: ownerUserInfo
        },
        stats: {
          members: membersCount || 0,
          transactions: transactionsCount || 0,
          rewards: rewardsCount || 0
        },
        recentTransactions: recentTransactions || []
      }
    }, {
      headers: {
        // Cache for 5 minutes for non-critical admin data
        'Cache-Control': 'private, max-age=300, s-maxage=300',
        // Add ETag for conditional requests
        'ETag': `"business-${id}-${Date.now()}"`,
        // CORS headers for admin routes
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    })

  } catch (error) {
    console.error('[Admin Business Details] Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/businesses/[id] - Update business
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Check if user is super admin
    const { isSuper, user } = await checkSuperAdmin()

    if (!isSuper || !user) {
      return NextResponse.json({ error: 'Unauthorized - Super admin access required' }, { status: 401 })
    }

    const body = await request.json()
    const supabase = getServiceRoleClient()

    console.log('[Admin Business Update] Super admin updating business:', id)

    const { data: updatedBusiness, error } = await supabase
      .from('companies')
      .update(body)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('[Admin Business Update] Error:', error)
      return NextResponse.json({ error: 'Failed to update business' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: updatedBusiness
    })

  } catch (error) {
    console.error('[Admin Business Update] Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/businesses/[id] - Delete business
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Check if user is super admin
    const { isSuper, user } = await checkSuperAdmin()

    if (!isSuper || !user) {
      return NextResponse.json({ error: 'Unauthorized - Super admin access required' }, { status: 401 })
    }

    const supabase = getServiceRoleClient()

    console.log('[Admin Business Delete] Super admin deleting business:', id)

    // TODO: Add cascade deletion logic for all related data
    // This is dangerous and should have proper confirmation
    const { error } = await supabase
      .from('companies')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('[Admin Business Delete] Error:', error)
      return NextResponse.json({ error: 'Failed to delete business' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Business deleted successfully'
    })

  } catch (error) {
    console.error('[Admin Business Delete] Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
