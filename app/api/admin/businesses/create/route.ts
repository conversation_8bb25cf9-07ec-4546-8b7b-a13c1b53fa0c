import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { PremiumBotCreationService } from '@/lib/services/premium-bot-creation'
import { z } from 'zod'
import crypto from 'crypto'

// Helper function to check if user is super admin
async function checkSuperAdmin() {
  const supabase = await createClient()
  const { data: { user }, error } = await supabase.auth.getUser()

  console.log('🔍 [checkSuperAdmin] Auth check:', {
    hasUser: !!user,
    userEmail: user?.email,
    appMetadata: user?.app_metadata,
    error: error?.message
  })

  if (error || !user) {
    console.log('❌ [checkSuperAdmin] No user or error')
    return { isSuper: false, user: null }
  }

  const isSuperAdmin = user.app_metadata?.is_super_admin === true ||
                      user.email === '<EMAIL>'

  console.log('🔍 [checkSuperAdmin] Super admin check:', {
    email: user.email,
    hasAppMetadata: !!user.app_metadata,
    isSuperAdminFromMetadata: user.app_metadata?.is_super_admin,
    isEmailMatch: user.email === '<EMAIL>',
    finalResult: isSuperAdmin
  })

  return { isSuper: isSuperAdmin, user }
}

// Validation schema for business creation
const createBusinessSchema = z.object({
  // Business Information
  businessName: z.string().min(1, 'Business name is required'),
  businessEmail: z.string().email('Valid business email is required'),
  businessPhone: z.string().min(1, 'Business phone is required'),
  businessAddress: z.string().optional(),

  // Owner Information
  ownerName: z.string().min(1, 'Owner name is required'),
  ownerEmail: z.string().email('Valid owner email is required'),
  ownerPhone: z.string().min(1, 'Owner phone is required'),

  // Bot Configuration (Premium tier)
  botTier: z.enum(['standard', 'premium']).default('premium'),
  botToken: z.string().optional(), // Required for premium tier

  // Business Settings
  pointsEarningRatio: z.number().min(0.1).max(100).default(1.0),
  currency: z.string().default('ETB'),

  // Branding (for premium bots)
  brandingConfig: z.object({
    welcomeMessage: z.string().optional(),
    primaryColor: z.string().optional(),
    logoUrl: z.preprocess((val) => val === '' ? undefined : val, z.string().url().optional())
  }).optional()
})

export async function POST(request: NextRequest) {
  try {
    // Check if user is super admin
    const { isSuper, user } = await checkSuperAdmin()

    if (!isSuper || !user) {
      return NextResponse.json({ error: 'Unauthorized - Super admin access required' }, { status: 401 })
    }

    const supabase = getServiceRoleClient()

    console.log('[Admin Business Creation] Super admin creating business:', user.email)

    // Parse and validate request body
    const body = await request.json()
    const validatedData = createBusinessSchema.parse(body)

    console.log('[Admin Business Creation] Creating business:', validatedData.businessName)

    // Generate temporary password for owner account
    const tempPassword = crypto.randomBytes(12).toString('base64').slice(0, 12)

    // Start transaction-like operations
    let createdUserId: string | null = null
    let createdCompanyId: string | null = null
    let botConfigId: string | null = null
    let shouldDeleteUser = false // Track if we created a new user

    try {
      // 1. Check if owner email already exists and handle accordingly
      const { data: existingAuth } = await supabase.auth.admin.listUsers()
      const emailExists = existingAuth?.users?.find(user => user.email === validatedData.ownerEmail)

      if (emailExists) {
        console.log('[Admin Business Creation] User already exists, using existing account:', emailExists.id)
        createdUserId = emailExists.id
        shouldDeleteUser = false
      } else {
        // Create new owner user account
        const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
          email: validatedData.ownerEmail,
          password: tempPassword,
          email_confirm: true,
          user_metadata: {
            name: validatedData.ownerName,
            phone: validatedData.ownerPhone,
            role: 'business_owner',
            created_by_admin: true,
            temp_password: true
          }
        })

        if (authError || !authUser.user) {
          throw new Error(`Failed to create owner account: ${authError?.message}`)
        }

        createdUserId = authUser.user.id
        shouldDeleteUser = true
        console.log('[Admin Business Creation] Created owner account:', createdUserId)
      }

      // 2. Create company record
      const companyData = {
        name: validatedData.businessName,
        slug: validatedData.businessName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
        administrator_id: createdUserId,
        points_earning_ratio: validatedData.pointsEarningRatio,
        bot_tier: validatedData.botTier,
        is_active: true,
        created_at: new Date().toISOString(),
        ...(validatedData.brandingConfig?.logoUrl && { logo_url: validatedData.brandingConfig.logoUrl }),
        ...(validatedData.brandingConfig?.primaryColor && { primary_color: validatedData.brandingConfig.primaryColor })
      }

      console.log('[Admin Business Creation] Attempting to create company with data:', companyData)

      // Use service role client for admin privileges
      const adminSupabase = getServiceRoleClient()

      try {
        const { data: company, error: companyError } = await adminSupabase
          .from('companies')
          .insert(companyData)
          .select()
          .single()

        if (companyError) {
          throw companyError
        }

        createdCompanyId = company.id
        console.log('[Admin Business Creation] Created company successfully:', createdCompanyId)

      } catch (insertError: unknown) {
        const error = insertError as Error
        console.log('[Admin Business Creation] Company creation failed with error:', error.message)
        throw new Error(`Failed to create company: ${error?.message}`)
      }

      // Ensure createdCompanyId is set
      if (!createdCompanyId) {
        throw new Error('Company creation failed - no company ID returned')
      }

      // Dashboard configurations are now created automatically via database trigger

      // 3. Setup premium bot if requested and token provided
      if (validatedData.botTier === 'premium' && validatedData.botToken) {
        console.log('[Admin Business Creation] Setting up premium bot...')

        try {
          const botService = new PremiumBotCreationService()
          const botResult = await botService.setupPremiumBotWithToken(
            createdCompanyId,
            validatedData.businessName,
            validatedData.botToken
          )

          if (botResult.success) {
            botConfigId = botResult.config_id
            console.log('[Admin Business Creation] Premium bot setup successful:', botConfigId)
          } else {
            console.error('[Admin Business Creation] Premium bot setup failed:', 'Bot setup failed')
            // Don't fail the entire process, but log the issue
          }
        } catch (botError) {
          console.error('[Admin Business Creation] Premium bot setup error:', botError)
          // Continue with business creation even if bot setup fails
          // This allows the business to be created and bot can be configured later
        }
      }

      // 4. Create default data in parallel for better performance
      const defaultTiers = [
        {
          company_id: createdCompanyId,
          tier_name: 'Bronze',
          minimum_points: 0,
          benefits_description: 'Welcome tier with basic benefits: 1x points earning, standard support',
          multiplier: 1.0
        },
        {
          company_id: createdCompanyId,
          tier_name: 'Silver',
          minimum_points: 250,
          benefits_description: 'Enhanced benefits for loyal customers: 1.2x points earning, priority support, 5% bonus on special offers',
          multiplier: 1.2
        },
        {
          company_id: createdCompanyId,
          tier_name: 'Gold',
          minimum_points: 500,
          benefits_description: 'Premium benefits for top customers: 1.5x points earning, VIP support, 10% bonus on special offers, early access to new products',
          multiplier: 1.5
        },
        {
          company_id: createdCompanyId,
          tier_name: 'Platinum',
          minimum_points: 1000,
          benefits_description: 'Elite benefits for VIP customers: 2x points earning, dedicated concierge service, 15% bonus on all purchases, exclusive event invitations',
          multiplier: 2.0
        }
      ]

      const defaultRewards = [
        {
          company_id: createdCompanyId,
          title: '10% Discount',
          description: 'Get 10% off your next purchase',
          points_required: 100,
          reward_type: 'DISCOUNT',
          discount_percentage: 10,
          is_active: true,
          expiration_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() // 1 year
        },
        {
          company_id: createdCompanyId,
          title: 'Free Item',
          description: 'Get a free item of your choice (up to 50 ETB value)',
          points_required: 500,
          reward_type: 'FREE_ITEM',
          is_active: true,
          expiration_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          company_id: createdCompanyId,
          title: '25% Discount',
          description: 'Get 25% off your entire purchase',
          points_required: 1000,
          reward_type: 'DISCOUNT',
          discount_percentage: 25,
          is_active: true,
          expiration_date: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]

      // Execute tier and reward creation in parallel for better performance
      const [tiersResult, rewardsResult] = await Promise.allSettled([
        supabase.from('tier_definitions').insert(defaultTiers),
        supabase.from('rewards').insert(defaultRewards)
      ])

      // Log results but don't fail the process if default data creation fails
      if (tiersResult.status === 'rejected') {
        console.error('[Admin Business Creation] Failed to create default tiers:', tiersResult.reason)
      } else if (tiersResult.value.error) {
        console.error('[Admin Business Creation] Failed to create default tiers:', tiersResult.value.error)
      }

      if (rewardsResult.status === 'rejected') {
        console.error('[Admin Business Creation] Failed to create default rewards:', rewardsResult.reason)
      } else if (rewardsResult.value.error) {
        console.error('[Admin Business Creation] Failed to create default rewards:', rewardsResult.value.error)
      }

      // 6. Create business owner invitation automatically
      console.log('[Admin Business Creation] Creating business owner invitation...')
      
      // Generate invitation token
      const invitationToken = crypto.randomBytes(16).toString('hex')
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 14) // Expires in 14 days

      // Create invitation record
      const { data: invitation, error: invitationError } = await supabase
        .from('business_owner_invitations')
        .insert({
          company_id: createdCompanyId,
          invited_by: user.id, // The super admin creating the business
          email: validatedData.ownerEmail,
          owner_name: validatedData.ownerName,
          phone_number: validatedData.ownerPhone,
          invitation_token: invitationToken,
          temp_password: tempPassword, // Same as user account password
          expires_at: expiresAt.toISOString(),
        })
        .select()
        .single()

      if (invitationError) {
        console.error('[Admin Business Creation] Failed to create invitation:', invitationError)
        // Don't fail the entire process, but log the issue
      }

      // Get company's bot configuration for invitation links
      interface BotConfig {
        bot_username: string;
      }
      
      interface CompanyBotData {
        bot_tier: string;
        bot_configuration: BotConfig | null;
      }
      
      const { data: companyBot } = await supabase
        .from('companies')
        .select(`
          bot_tier,
          bot_configuration:bot_configuration_id(bot_username)
        `)
        .eq('id', createdCompanyId)
        .single() as { data: CompanyBotData | null }

      // Use company-specific bot if premium, otherwise fall back to standard bot
      let botUsername = '' as string
      if (companyBot?.bot_tier === 'premium' && companyBot.bot_configuration?.bot_username) {
        botUsername = companyBot.bot_configuration.bot_username
        console.log('🤖 Using premium bot for business owner invitation:', botUsername)
      } else {
        botUsername = process.env.TELEGRAM_BOT_USERNAME || 'Loyal_ET_bot'
        console.log('🤖 Using standard bot for business owner invitation:', botUsername)
      }

      // Create invitation links
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
      const invitationLink = `${baseUrl}/business-owners/accept?token=${invitationToken}`
      const telegramLink = botUsername ? `https://t.me/${botUsername}?start=business_invite_${invitationToken}` : null

      console.log('[Admin Business Creation] Business creation completed successfully with invitation')

      return NextResponse.json({
        success: true,
        message: 'Business created successfully with owner invitation',
        data: {
          company: {
            id: createdCompanyId,
            name: validatedData.businessName,
            slug: validatedData.businessName.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
            bot_tier: validatedData.botTier
          },
          owner: {
            id: createdUserId,
            email: validatedData.ownerEmail,
            name: validatedData.ownerName,
            tempPassword: tempPassword
          },
          invitation: invitation ? {
            id: invitation.id,
            token: invitationToken,
            invitationLink,
            telegramLink,
            expiresAt: invitation.expires_at
          } : null,
          bot: botConfigId ? {
            configId: botConfigId,
            tier: validatedData.botTier,
            status: 'configured'
          } : {
            tier: validatedData.botTier,
            status: validatedData.botTier === 'standard' ? 'using_shared_bot' : 'not_configured'
          }
        }
      })

    } catch (setupError) {
      // Rollback created resources
      console.error('[Admin Business Creation] Setup failed, rolling back:', setupError)

      // Delete bot configuration if created
      if (botConfigId) {
        await supabase
          .from('bot_configurations')
          .delete()
          .eq('id', botConfigId)
      }

      // Delete company if created
      if (createdCompanyId) {
        await supabase
          .from('companies')
          .delete()
          .eq('id', createdCompanyId)
      }

      // Delete user if created (not existing)
      if (createdUserId && shouldDeleteUser) {
        await supabase.auth.admin.deleteUser(createdUserId)
      }

      throw setupError
    }

  } catch (error) {
    console.error('[Admin Business Creation] Error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to create business',
      success: false
    }, { status: 500 })
  }
}

// Get all businesses (admin only)
export async function GET(request: NextRequest) {
  try {
    // Check if user is super admin
    const { isSuper, user } = await checkSuperAdmin()

    if (!isSuper || !user) {
      return NextResponse.json({ error: 'Unauthorized - Super admin access required' }, { status: 401 })
    }

    const supabase = getServiceRoleClient()

    console.log('[Admin Business List] Super admin fetching businesses:', user.email)

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50') // Increased default limit for better UX
    const search = searchParams.get('search') || ''

    // Build optimized query with proper indexing hints
    let query = supabase
      .from('companies')
      .select(`
        id,
        name,
        slug,
        bot_tier,
        bot_configuration_id,
        is_active,
        created_at,
        points_earning_ratio,
        administrator_id,
        business_type,
        onboarding_completed,
        bot_configurations:bot_configuration_id (
          id,
          bot_username,
          is_active,
          last_activity,
          message_count
        )
      `, { count: 'exact' })
      .order('created_at', { ascending: false })

    // Add search filter if provided
    if (search) {
      query = query.or(`name.ilike.%${search}%, slug.ilike.%${search}%`)
    }

    // Add pagination only if not searching (for better caching)
    let businessesQuery = query
    if (!search) {
      const from = (page - 1) * limit
      const to = from + limit - 1
      businessesQuery = query.range(from, to)
    }

    const { data: businesses, error, count } = await businessesQuery

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      data: businesses || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    }, {
      headers: {
        // Cache business list for 2 minutes for admin routes
        'Cache-Control': 'private, max-age=120, s-maxage=120',
        // Add ETag for conditional requests
        'ETag': `"businesses-${page}-${limit}-${search}-${Date.now()}"`,
        // CORS headers
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        // Performance hints
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY'
      }
    })

  } catch (error) {
    console.error('[Admin Business List] Error:', error)
    return NextResponse.json({
      error: 'Failed to fetch businesses',
      success: false
    }, { status: 500 })
  }
}
