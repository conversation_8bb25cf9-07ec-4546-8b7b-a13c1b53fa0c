import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { getUserIdFromSession } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'

// GET /api/business-owners/invite/[id] - Get specific invitation details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient()
    const serviceClient = getServiceRoleClient()
    const { id: invitationId } = await params

    if (!invitationId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      )
    }

    // Get current user ID and check if super admin
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: { user } } = await supabase.auth.getUser()
    const isSuperAdmin = user?.email === '<EMAIL>' ||
                        user?.app_metadata?.is_super_admin === 'true'

    if (!isSuperAdmin) {
      return NextResponse.json(
        { error: 'Only super admins can view invitation details' },
        { status: 403 }
      )
    }

    // Get invitation details
    const { data: invitation, error } = await serviceClient
      .from('business_owner_invitations')
      .select('*')
      .eq('id', invitationId)
      .single()

    if (error || !invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      )
    }

    // Fetch company data
    const { data: company } = await serviceClient
      .from('companies')
      .select('name')
      .eq('id', invitation.company_id)
      .maybeSingle()

    // Fetch user data
    const { data: invitedByUser } = await serviceClient
      .from('users')
      .select('email')
      .eq('id', invitation.invited_by)
      .maybeSingle()

    return NextResponse.json({
      invitation: {
        id: invitation.id,
        email: invitation.email,
        owner_name: invitation.owner_name,
        phone_number: invitation.phone_number,
        created_at: invitation.created_at,
        expires_at: invitation.expires_at,
        used_at: invitation.used_at,
        invitation_token: invitation.invitation_token,
        temp_password: invitation.temp_password,
        companies: company ? { name: company.name } : { name: 'Unknown Company' },
        users: invitedByUser ? { email: invitedByUser.email } : { email: 'Unknown User' }
      }
    })

  } catch (error) {
    console.error('Error fetching business owner invitation details:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
