import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'
import { getUserRole, getUserIdFromSession } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'
import { randomBytes } from 'crypto'

// Validation schema for business owner invitation
const businessOwnerInviteSchema = z.object({
  email: z.string().email('Valid email is required'),
  ownerName: z.string().min(1, 'Owner name is required'),
  phoneNumber: z.string().optional(),
  companyId: z.string().uuid('Valid company ID is required'),
})

// POST /api/business-owners/invite - Create business owner invitation
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()
    const serviceClient = getServiceRoleClient()

    const body = await request.json()
    const validation = businessOwnerInviteSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      )
    }

    const { email, ownerName, phoneNumber, companyId } = validation.data

    // Get current user ID
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is authorized (super admin or company owner)
    const { data: { user } } = await supabase.auth.getUser()
    const isSuperAdmin = user?.email === '<EMAIL>' ||
                        user?.app_metadata?.is_super_admin === 'true'

    if (!isSuperAdmin) {
      const userRole = await getUserRole(supabase, companyId)
      if (userRole !== 'OWNER') {
        return NextResponse.json(
          { error: 'Only super admins or company owners can invite business owners' },
          { status: 403 }
        )
      }
    }

    // Check if email is already a user in the system
    const { data: existingUsers } = await serviceClient.auth.admin.listUsers()
    const existingUser = existingUsers?.users?.find(user => user.email === email)

    if (existingUser) {
      // Check if they're already associated with this company
      const { data: existingAdmin } = await serviceClient
        .from('company_administrators')
        .select('role')
        .eq('administrator_id', existingUser.id)
        .eq('company_id', companyId)
        .single()

      if (existingAdmin) {
        return NextResponse.json(
          { error: `User is already a ${existingAdmin.role.toLowerCase()} for this company` },
          { status: 400 }
        )
      }
    }

    // Generate unique invitation token and temporary password
    const invitationToken = randomBytes(16).toString('hex')
    const tempPassword = randomBytes(8).toString('base64').replace(/[^a-zA-Z0-9]/g, '').substring(0, 12)
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 14) // Expires in 14 days for business owners

    // Create invitation record
    const { data: invitation, error: insertError } = await serviceClient
      .from('business_owner_invitations')
      .insert({
        company_id: companyId,
        invited_by: userId,
        email,
        owner_name: ownerName,
        phone_number: phoneNumber,
        invitation_token: invitationToken,
        temp_password: tempPassword,
        expires_at: expiresAt.toISOString(),
      })
      .select()
      .single()

    if (insertError) {
      if (insertError.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'Invitation already sent to this email for this company' },
          { status: 400 }
        )
      }

      console.error('Failed to create business owner invitation:', insertError)
      return NextResponse.json(
        { error: 'Failed to create invitation' },
        { status: 500 }
      )
    }

    // Get company's bot configuration for premium bot support
    interface BotConfig {
      bot_username: string;
    }
    
    interface CompanyBotData {
      bot_tier: string;
      bot_configuration: BotConfig | null;
    }
    
    const { data: companyBot } = await serviceClient
      .from('companies')
      .select(`
        bot_tier,
        bot_configuration:bot_configuration_id(bot_username)
      `)
      .eq('id', companyId)
      .single() as { data: CompanyBotData | null }

    // Use company-specific bot if premium, otherwise fall back to standard bot
    let botUsername = '' as string
    if (companyBot?.bot_tier === 'premium' && companyBot.bot_configuration?.bot_username) {
      botUsername = companyBot.bot_configuration.bot_username
      console.log('🤖 Using premium bot for business owner invitation:', botUsername)
    } else {
      botUsername = process.env.TELEGRAM_BOT_USERNAME || 'Loyal_ET_bot'
      console.log('🤖 Using standard bot for business owner invitation:', botUsername)
    }

    if (!botUsername) {
      return NextResponse.json(
        { error: 'Bot not configured' },
        { status: 500 }
      )
    }

    // Create Telegram deep link with business_invite prefix for business owner invitations
    const telegramLink = `https://t.me/${botUsername}?start=business_invite_${invitationToken}`

    // Create direct invitation link for business owner signup
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const invitationLink = `${baseUrl}/business-owners/accept?token=${invitationToken}`

    // Get company info for notification purposes
    const { data: company } = await serviceClient
      .from('companies')
      .select('name')
      .eq('id', companyId)
      .single()

    return NextResponse.json({
      success: true,
      invitation: {
        id: invitation.id,
        email,
        ownerName,
        phoneNumber,
        expiresAt: invitation.expires_at,
        invitationToken,
        invitationLink,
        telegramLink,
        tempPassword // Include for initial sharing (should be shared securely)
      },
      message: `Business owner invitation created for ${ownerName} (${email})`,
      companyName: company?.name || 'Unknown Company'
    })

  } catch (error) {
    console.error('Business owner invitation error:', error)
    return NextResponse.json(
      { error: 'Internal error' },
      { status: 500 }
    )
  }
}

// GET /api/business-owners/invite - List invitations for super admin or check specific invitation
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()
    const serviceClient = getServiceRoleClient()
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')
    const listAll = searchParams.get('listAll') === 'true'

    // If token is provided, check specific invitation status
    if (token) {
      // Check if invitation exists and is valid
      const { data: invitation, error } = await serviceClient
        .from('business_owner_invitations')
        .select(`
          *,
          companies(name),
          users!invited_by(name, email)
        `)
        .eq('invitation_token', token)
        .gt('expires_at', new Date().toISOString())
        .is('used_at', null)
        .single()

      if (error || !invitation) {
        return NextResponse.json(
          { error: 'Invalid or expired invitation' },
          { status: 404 }
        )
      }

      // Don't return temp_password in GET request for security
      return NextResponse.json({
        valid: true,
        invitation: {
          id: invitation.id,
          email: invitation.email,
          ownerName: invitation.owner_name,
          phoneNumber: invitation.phone_number,
          companyName: invitation.companies.name,
          invitedBy: invitation.users?.name || invitation.users?.email,
          expiresAt: invitation.expires_at
        }
      })
    }

    // If listAll is true, return all invitations for super admin
    if (listAll) {
      // Get current user ID and check if super admin
      const userId = await getUserIdFromSession(supabase)
      if (!userId) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      const { data: { user } } = await supabase.auth.getUser()
      const isSuperAdmin = user?.email === '<EMAIL>' ||
                          user?.app_metadata?.is_super_admin === 'true'

      if (!isSuperAdmin) {
        return NextResponse.json(
          { error: 'Only super admins can list all invitations' },
          { status: 403 }
        )
      }

      // Get all business owner invitations
      const { data: invitations, error } = await serviceClient
        .from('business_owner_invitations')
        .select(`
          id,
          email,
          owner_name,
          phone_number,
          created_at,
          expires_at,
          used_at,
          company_id,
          invited_by
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching business owner invitations:', error)
        return NextResponse.json(
          { error: 'Failed to fetch invitations' },
          { status: 500 }
        )
      }

      // If we have invitations, fetch the related company and user data separately
      if (invitations && invitations.length > 0) {
        // Get unique company IDs and user IDs
        const companyIds = [...new Set(invitations.map(inv => inv.company_id))];
        const userIds = [...new Set(invitations.map(inv => inv.invited_by))];
        
        // Fetch company data
        const { data: companies } = await serviceClient
          .from('companies')
          .select('id, name')
          .in('id', companyIds);
          
        // Fetch user data
        const { data: users } = await serviceClient
          .from('users')
          .select('id, email')
          .in('id', userIds);
          
        // Create lookup maps for faster access
        type CompanyData = { id: string; name: string };
        type UserData = { id: string; email: string };
        
        interface CompanyMap {
          [key: string]: CompanyData;
        }
        
        interface UserMap {
          [key: string]: UserData;
        }
        
        const companyMap = companies ? companies.reduce((map: CompanyMap, company: CompanyData) => {
          map[company.id] = company;
          return map;
        }, {} as CompanyMap) : {} as CompanyMap;
        
        const userMap = users ? users.reduce((map: UserMap, user: UserData) => {
          map[user.id] = user;
          return map;
        }, {} as UserMap) : {} as UserMap;
        
        // Enrich invitations with company and user data
        const enrichedInvitations = invitations.map(invitation => ({
          ...invitation,
          companies: companyMap[invitation.company_id] ? { name: companyMap[invitation.company_id].name } : { name: 'Unknown Company' },
          users: userMap[invitation.invited_by] ? { email: userMap[invitation.invited_by].email } : { email: 'Unknown User' }
        }));
        
        return NextResponse.json({ invitations: enrichedInvitations });
      }

      return NextResponse.json({ invitations })
    }

    return NextResponse.json({ error: 'Token or listAll parameter required' }, { status: 400 })

  } catch (error) {
    console.error('Business owner invitation error:', error)
    return NextResponse.json(
      { error: 'Internal error' },
      { status: 500 }
    )
  }
}

// DELETE /api/business-owners/invite - Cancel a business owner invitation
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()
    const serviceClient = getServiceRoleClient()

    const url = new URL(request.url)
    const invitationId = url.searchParams.get('id')

    if (!invitationId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      )
    }

    // Get current user ID
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is super admin
    const { data: { user } } = await supabase.auth.getUser()
    const isSuperAdmin = user?.email === '<EMAIL>' ||
                        user?.app_metadata?.is_super_admin === 'true'

    if (!isSuperAdmin) {
      return NextResponse.json(
        { error: 'Only super admins can cancel business owner invitations' },
        { status: 403 }
      )
    }

    // Get invitation details to verify it exists
    const { data: invitation, error: fetchError } = await serviceClient
      .from('business_owner_invitations')
      .select('id, email, used_at')
      .eq('id', invitationId)
      .single()

    if (fetchError || !invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      )
    }

    // For accepted invitations, we'll allow deletion but log it for audit purposes
    if (invitation.used_at) {
      console.log(`Deleting an accepted business owner invitation: ${invitationId} for email: ${invitation.email}`)
    }

    // Delete the invitation
    const { error: deleteError } = await serviceClient
      .from('business_owner_invitations')
      .delete()
      .eq('id', invitationId)

    if (deleteError) {
      console.error('Error deleting business owner invitation:', deleteError)
      return NextResponse.json(
        { error: 'Failed to cancel invitation' },
        { status: 500 }
      )
    }

    // Return appropriate message based on whether invitation was accepted or pending
    return NextResponse.json({
      message: invitation.used_at 
        ? 'Business owner invitation deleted successfully' 
        : 'Business owner invitation cancelled successfully'
    })

  } catch (error) {
    console.error('Error cancelling business owner invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
