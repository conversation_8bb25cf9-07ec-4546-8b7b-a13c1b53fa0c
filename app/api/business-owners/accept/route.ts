import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'

// Validation schema for accepting business owner invitation
const acceptInvitationSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
})

// POST /api/business-owners/accept - Accept business owner invitation and create account
export async function POST(request: NextRequest) {
  try {
    const serviceClient = getServiceRoleClient()
    const body = await request.json()
    const validation = acceptInvitationSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      )
    }

    const { token, password } = validation.data

    // Find the invitation
    const { data: invitation, error: invitationError } = await serviceClient
      .from('business_owner_invitations')
      .select('*')
      .eq('invitation_token', token)
      .gt('expires_at', new Date().toISOString())
      .is('used_at', null)
      .single()

    if (invitationError || !invitation) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation' },
        { status: 404 }
      )
    }

    // Check if user already exists
    const { data: existingUsers } = await serviceClient.auth.admin.listUsers()
    const existingUser = existingUsers?.users?.find(user => user.email === invitation.email)

    let userId: string
    let isExistingUser = false

    if (existingUser) {
      console.log('🔄 Updating existing user password for business owner invitation')
      console.log('Existing user details:', {
        id: existingUser.id,
        email: existingUser.email,
        emailConfirmed: existingUser.email_confirmed_at
      })

      isExistingUser = true

      // Update existing user's password and metadata
      const { error: updateError } = await serviceClient.auth.admin.updateUserById(
        existingUser.id,
        {
          password,
          email_confirm: true, // Ensure email is confirmed
          user_metadata: {
            ...existingUser.user_metadata,
            name: invitation.owner_name,
            phone_number: invitation.phone_number,
            role: 'business_owner',
            temp_password: false, // Mark that temp password has been used
            password_change_required: true, // Force password change on first login
            invitation_accepted: true,
            invitation_accepted_at: new Date().toISOString()
          }
        }
      )

      if (updateError) {
        console.error('Failed to update existing user password:', updateError)
        console.error('Update error details:', {
          message: updateError.message,
          status: updateError.status,
          name: updateError.name
        })
        return NextResponse.json(
          { error: `Failed to update user account: ${updateError.message}` },
          { status: 500 }
        )
      }

      userId = existingUser.id
      console.log('✅ Existing user updated successfully:', { id: userId, email: invitation.email })
    } else {
      console.log('🆕 Creating new user for business owner invitation')

      // Create new user account
      const { data: newUser, error: createError } = await serviceClient.auth.admin.createUser({
        email: invitation.email,
        password,
        email_confirm: true, // Auto-confirm email to avoid verification step
        user_metadata: {
          name: invitation.owner_name,
          phone_number: invitation.phone_number,
          role: 'business_owner',
          temp_password: false,
          password_change_required: true, // Force password change on first login
          invitation_accepted: true,
          invitation_accepted_at: new Date().toISOString()
        }
      })

      if (createError) {
        console.error('Failed to create new user:', createError)
        console.error('Create error details:', {
          message: createError.message,
          status: createError.status,
          name: createError.name
        })
        return NextResponse.json(
          { error: `Failed to create user account: ${createError.message}` },
          { status: 500 }
        )
      }

      if (!newUser.user) {
        console.error('User creation returned no user data')
        return NextResponse.json(
          { error: 'Failed to create user account - no user data returned' },
          { status: 500 }
        )
      }

      userId = newUser.user.id
      console.log('✅ New user created successfully:', { id: userId, email: invitation.email })
    }

    // Get the administrator ID from the administrators table
    const { data: adminRecord } = await serviceClient
      .from('administrators')
      .select('id')
      .eq('email', invitation.email)
      .maybeSingle()

    if (adminRecord) {
      // Check if user is already a company administrator using the correct administrator ID
      const { data: existingAdmin } = await serviceClient
        .from('company_administrators')
        .select('id, role')
        .eq('administrator_id', adminRecord.id)
        .eq('company_id', invitation.company_id)
        .maybeSingle()

      if (!existingAdmin) {
        console.log('👤 Adding user as company administrator with OWNER role')

        // Add user as company administrator with OWNER role using administrator ID
        const { error: adminError } = await serviceClient
          .from('company_administrators')
          .insert({
            administrator_id: adminRecord.id,
            company_id: invitation.company_id,
            role: 'OWNER'
          })

        if (adminError) {
          console.error('Failed to create company administrator:', adminError)
          return NextResponse.json(
            { error: 'Failed to associate user with company' },
            { status: 500 }
          )
        }
      } else {
        console.log('✅ User already exists as company administrator with role:', existingAdmin.role)
      }
    } else {
      console.log('⚠️ Administrator record not found, skipping company administrator creation')
    }

    // Mark invitation as used
    const { error: updateInvitationError } = await serviceClient
      .from('business_owner_invitations')
      .update({ used_at: new Date().toISOString() })
      .eq('id', invitation.id)

    if (updateInvitationError) {
      console.error('Failed to mark invitation as used:', updateInvitationError)
      // Don't fail the request if this update fails
    }

    // Get company details for response
    const { data: company } = await serviceClient
      .from('companies')
      .select('name, slug, onboarding_completed, setup_wizard_step')
      .eq('id', invitation.company_id)
      .maybeSingle()

    // Create a session for the user to enable seamless login
    let sessionLink = null
    try {
      const { data: sessionData, error: sessionError } = await serviceClient.auth.admin.generateLink({
        type: 'magiclink',
        email: invitation.email,
        options: {
          redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`
        }
      })

      if (sessionError) {
        console.error('Failed to generate session link:', sessionError)
      } else {
        sessionLink = sessionData?.properties?.action_link
        console.log('✅ Generated session link successfully')
      }
    } catch (linkError) {
      console.error('Error generating session link:', linkError)
    }

    return NextResponse.json({
      success: true,
      message: isExistingUser
        ? 'Business owner account updated successfully'
        : 'Business owner account created successfully',
      user: {
        id: userId,
        email: invitation.email,
        name: invitation.owner_name,
        role: 'OWNER',
        isExistingUser
      },
      company: {
        id: invitation.company_id,
        name: company?.name || 'Unknown Company',
        slug: company?.slug || 'unknown',
        onboarding_completed: company?.onboarding_completed || false,
        setup_wizard_step: company?.setup_wizard_step || 1
      },
      sessionLink: sessionLink,
      nextStep: company?.onboarding_completed
        ? 'dashboard'
        : 'onboarding'
    })

  } catch (error) {
    console.error('Accept business owner invitation error:', error)
    return NextResponse.json(
      { error: 'Internal error' },
      { status: 500 }
    )
  }
}

// GET /api/business-owners/accept - Get invitation details for acceptance page
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({ error: 'Token required' }, { status: 400 })
    }

    const serviceClient = getServiceRoleClient()

    // Check if invitation exists and is valid
    const { data: invitation, error } = await serviceClient
      .from('business_owner_invitations')
      .select(`
        *,
        companies(name, slug)
      `)
      .eq('invitation_token', token)
      .gt('expires_at', new Date().toISOString())
      .is('used_at', null)
      .single()

    if (error || !invitation) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      valid: true,
      invitation: {
        id: invitation.id,
        email: invitation.email,
        ownerName: invitation.owner_name,
        phoneNumber: invitation.phone_number,
        companyName: invitation.companies.name,
        companySlug: invitation.companies.slug,
        invitedBy: 'System Admin',
        expiresAt: invitation.expires_at,
        tempPassword: invitation.temp_password // Include for auto-fill option
      }
    })

  } catch (error) {
    console.error('Business owner invitation check error:', error)
    return NextResponse.json(
      { error: 'Internal error' },
      { status: 500 }
    )
  }
}
