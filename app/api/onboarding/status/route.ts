import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Use enhanced onboarding function with detailed progress tracking
    const { data: onboardingData, error: onboardingError } = await supabase.rpc('get_enhanced_onboarding_status', {
      p_user_id: userId
    })

    if (onboardingError) {
      console.error('Error fetching enhanced onboarding status:', onboardingError)
      return NextResponse.json(
        { error: `Failed to fetch onboarding status: ${onboardingError.message}` },
        { status: 500 }
      )
    }

    if (!onboardingData) {
      // Fallback to basic status if function doesn't exist yet
      return NextResponse.json({
        hasCompany: false,
        isOnboardingComplete: false,
        completionPercentage: 0,
        progress: {
          essential: 0,
          business: 0,
          advanced: 0,
          overall: 0
        },
        nextSteps: ['create_company']
      })
    }

    // Return the enhanced data structure
    const enhancedData = {
      ...onboardingData
    }

    return NextResponse.json(enhancedData)

  } catch (error) {
    console.error('Unexpected error in onboarding status:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId, stepId, completed, skipped } = await request.json()

    if (!userId || !stepId) {
      return NextResponse.json(
        { error: 'User ID and step ID are required' },
        { status: 400 }
      )
    }

    // Get the user's company
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('id, onboarding_step_completed, onboarding_skipped_steps')
      .eq('administrator_id', userId)
      .single()

    if (companyError || !company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Update step completion
    const currentSteps = company.onboarding_step_completed || {}
    const currentSkipped = company.onboarding_skipped_steps || []

    const updatedSteps = { ...currentSteps }
    let updatedSkipped = [...currentSkipped]

    if (completed !== undefined) {
      updatedSteps[stepId] = completed
      // Remove from skipped if marked as completed
      if (completed) {
        updatedSkipped = updatedSkipped.filter(skip => skip !== stepId)
      }
    }

    if (skipped !== undefined && skipped) {
      // Add to skipped if not already there
      if (!updatedSkipped.includes(stepId)) {
        updatedSkipped.push(stepId)
      }
      // Remove from completed if skipped
      delete updatedSteps[stepId]
    }

    // Update the database
    const { error: updateError } = await supabase
      .from('companies')
      .update({
        onboarding_step_completed: updatedSteps,
        onboarding_skipped_steps: updatedSkipped,
        setup_wizard_step: Math.max(
          Object.keys(updatedSteps).filter(key => updatedSteps[key]).length + 1,
          1
        )
      })
      .eq('id', company.id)

    if (updateError) {
      console.error('Error updating onboarding step:', updateError)
      return NextResponse.json(
        { error: 'Failed to update onboarding step' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      stepId,
      completed: completed || false,
      skipped: skipped || false
    })

  } catch (error) {
    console.error('Setup step update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
