'use client'

import { useCompany } from '@/contexts/company-context'
import { useRequireAuth } from '@/hooks/use-auth'
import { useOnboardingStatus } from '@/hooks/use-onboarding'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { CheckCircle, Circle, ArrowRight, Users, Gift, CreditCard, LucideIcon, Trophy, Settings } from 'lucide-react'
import Link from 'next/link'
import { useMemo } from 'react'

interface OnboardingStep {
  id: string
  title: string
  description: string
  completed: boolean
  href: string
  icon: LucideIcon
}

export default function WelcomeState() {
  const { company } = useCompany()
  const { user } = useRequireAuth()
  const { data: onboardingData, isLoading } = useOnboardingStatus(user?.id)

  // Derive onboarding steps from API data using TanStack Query
  const onboardingSteps: OnboardingStep[] = useMemo(() => [
    {
      id: 'account',
      title: 'Account Created',
      description: 'Your loyalty program account is ready',
      completed: true,
      href: '/dashboard',
      icon: CheckCircle
    },
    {
      id: 'branding',
      title: 'Company Branding',
      description: 'Add your logo and customize colors',
      completed: onboardingData?.company?.hasLogo || onboardingData?.company?.hasCustomColor || false,
      href: '/settings/appearance',
      icon: Settings
    },
    {
      id: 'tiers',
      title: 'Set Up Loyalty Tiers',
      description: 'Configure Bronze, Silver, and Gold tiers',
      completed: (onboardingData?.company?.tierCount || 0) > 0,
      href: '/tiers',
      icon: Trophy
    },
    {
      id: 'reward',
      title: 'Create Your First Reward',
      description: 'Set up a reward for your customers',
      completed: (onboardingData?.company?.rewardCount || 0) > 0,
      href: '/rewards',
      icon: Gift
    },
    {
      id: 'customer',
      title: 'Add Your First Customer',
      description: 'Add a customer to your loyalty program',
      completed: (onboardingData?.company?.memberCount || 0) > 0,
      href: '/members',
      icon: Users
    },
    {
      id: 'transaction',
      title: 'Process First Transaction',
      description: 'Award points for a purchase',
      completed: (onboardingData?.company?.transactionCount || 0) > 0,
      href: '/transactions',
      icon: CreditCard
    }
  ], [onboardingData])

  const progressPercentage = onboardingData?.progress?.overall || 0
  const progressBreakdown = onboardingData?.progress || {
    essential: 0,
    business: 0,
    advanced: 0,
    overall: 0
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-32 bg-muted rounded-lg"></div>
          <div className="h-48 bg-muted rounded-lg"></div>
          <div className="h-32 bg-muted rounded-lg"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <Card className="premium-card border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
        <CardHeader className="text-center pb-4">
          <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-primary" />
          </div>
          <CardTitle className="text-2xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Welcome to {company?.name || 'Your Business'}!
          </CardTitle>
          <CardDescription className="text-base">
            Your loyalty program is ready to go. Let&apos;s get you set up with the essential features.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Overview */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2">
              <span className="text-sm font-medium">Setup Progress</span>
              <Badge variant="secondary" className="text-xs">
                {progressPercentage}% complete
              </Badge>
            </div>
            <Progress value={progressPercentage} className="h-2" />

            {/* Enhanced Progress Breakdown */}
            <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground mt-2">
              <div className="text-center">
                <div className="font-medium text-blue-600">Essential</div>
                <div>{progressBreakdown.essential}%</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-green-600">Business</div>
                <div>{progressBreakdown.business}%</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-purple-600">Advanced</div>
                <div>{progressBreakdown.advanced}%</div>
              </div>
            </div>

            <p className="text-xs text-muted-foreground">
              {progressPercentage >= 60
                ? "🎉 Your loyalty program is ready to use!"
                : progressPercentage >= 40
                  ? "👍 Great progress! Complete a few more steps."
                  : "🚀 Let's get your loyalty program set up!"
              }
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Getting Started Checklist */}
      <Card className="premium-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Circle className="h-5 w-5 text-primary" />
            Getting Started Checklist
          </CardTitle>
          <CardDescription>
            Complete these steps to start using your loyalty program effectively
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {onboardingSteps.map((step) => {
            const Icon = step.icon
            return (
              <div
                key={step.id}
                className={`flex items-center gap-4 p-4 rounded-lg border transition-all ${
                  step.completed
                    ? 'bg-primary/5 border-primary/20'
                    : 'bg-muted/30 border-border hover:bg-muted/50'
                }`}
              >
                <div className={`flex-shrink-0 ${step.completed ? 'text-primary' : 'text-muted-foreground'}`}>
                  <Icon className="h-5 w-5" />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className={`font-medium ${step.completed ? 'text-primary' : 'text-foreground'}`}>
                    {step.title}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {step.description}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  {step.completed ? (
                    <CheckCircle className="h-5 w-5 text-primary" />
                  ) : (
                    <Button asChild size="sm" variant="outline">
                      <Link href={step.href} className="flex items-center gap-1">
                        Start
                        <ArrowRight className="h-3 w-3" />
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            )
          })}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="premium-card">
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Get started with the most common tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button asChild className="h-auto p-4 flex-col gap-2">
              <Link href="/tiers">
                <Trophy className="h-6 w-6" />
                <span className="font-medium">Manage Tiers</span>
                <span className="text-xs opacity-80">Configure loyalty tiers</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto p-4 flex-col gap-2">
              <Link href="/rewards">
                <Gift className="h-6 w-6" />
                <span className="font-medium">Create Reward</span>
                <span className="text-xs opacity-80">Set up your first reward</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto p-4 flex-col gap-2">
              <Link href="/members">
                <Users className="h-6 w-6" />
                <span className="font-medium">Add Customer</span>
                <span className="text-xs opacity-80">Register your first member</span>
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-auto p-4 flex-col gap-2">
              <Link href="/transactions">
                <CreditCard className="h-6 w-6" />
                <span className="font-medium">Award Points</span>
                <span className="text-xs opacity-80">Process a transaction</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
