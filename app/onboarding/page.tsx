'use client'

import { Suspense, useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompleteOnboarding } from '@/hooks/use-onboarding'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { CheckCircle, Building2, Users, Settings, Zap } from 'lucide-react'
import { toast } from 'sonner'

const onboardingSteps = [
  {
    id: 1,
    title: 'Welcome to Loyal ET',
    description: 'Get started with your loyalty program',
    icon: Building2,
    content: 'Welcome to your business owner dashboard! Let&apos;s set up your loyalty program to start engaging with your customers.'
  },
  {
    id: 2,
    title: 'Configure Your Program',
    description: 'Set up points, rewards, and tiers',
    icon: Settings,
    content: 'Configure your loyalty program settings including point earning ratios, reward tiers, and customer benefits.'
  },
  {
    id: 3,
    title: 'Invite Your Team',
    description: 'Add cashiers and staff members',
    icon: Users,
    content: 'Invite cashiers and staff members to help manage your loyalty program and serve customers.'
  },
  {
    id: 4,
    title: 'You\'re All Set!',
    description: 'Start engaging with customers',
    icon: Zap,
    content: 'Congratulations! Your loyalty program is ready. Start adding customers and tracking their engagement.'
  }
]

export default function OnboardingPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    }>
      <OnboardingContent />
    </Suspense>
  )
}

function OnboardingContent() {
  const { user, isLoading } = useRequireAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const stepParam = searchParams.get('step')
  const completeOnboardingMutation = useCompleteOnboarding()

  const [currentStep, setCurrentStep] = useState(parseInt(stepParam || '1'))
  const [completing, setCompleting] = useState(false)  // Redirect if user is not a business owner
  useEffect(() => {
    if (!isLoading && user && user.user_metadata?.role !== 'business_owner') {
      router.push('/dashboard')
    }
  }, [user, isLoading, router])

  const handleNext = () => {
    if (currentStep < onboardingSteps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = async () => {
    setCompleting(true)

    try {
      // Use the enhanced completion logic
      const result = await completeOnboardingMutation.mutateAsync()

      toast.success('🎉 Onboarding completed! Welcome to Loyal ET!')
      console.log('✅ Onboarding completed:', result)

      router.push('/dashboard')
    } catch (error: unknown) {
      console.error('Error completing onboarding:', error)

      // Handle specific error cases
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      if (errorMessage.includes('Minimum onboarding requirements not met')) {
        toast.error('Please complete the basic setup steps first')
        router.push('/dashboard') // Redirect to dashboard to complete setup
      } else {
        toast.error('Failed to complete onboarding. Please try again.')
      }
    } finally {
      setCompleting(false)
    }
  }

  const handleSkip = () => {
    router.push('/dashboard')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  const currentStepData = onboardingSteps[currentStep - 1]
  const progress = (currentStep / onboardingSteps.length) * 100
  const Icon = currentStepData.icon

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-amber-50 dark:from-purple-950 dark:to-amber-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <Icon className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-2xl font-bold">
            {currentStepData.title}
          </CardTitle>
          <CardDescription className="text-lg">
            {currentStepData.description}
          </CardDescription>

          {/* Progress bar */}
          <div className="mt-6">
            <div className="flex justify-between text-sm text-muted-foreground mb-2">
              <span>Step {currentStep} of {onboardingSteps.length}</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Step content */}
          <div className="text-center py-8">
            <p className="text-muted-foreground text-lg leading-relaxed">
              {currentStepData.content}
            </p>
          </div>

          {/* Step indicators */}
          <div className="flex justify-center space-x-2">
            {onboardingSteps.map((step, index) => (
              <div
                key={step.id}
                className={`w-3 h-3 rounded-full ${
                  index + 1 <= currentStep
                    ? 'bg-primary'
                    : 'bg-muted'
                }`}
              />
            ))}
          </div>

          {/* Navigation buttons */}
          <div className="flex justify-between pt-6">
            <div className="flex space-x-2">
              {currentStep > 1 && (
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={completing}
                >
                  Previous
                </Button>
              )}
              <Button
                variant="ghost"
                onClick={handleSkip}
                disabled={completing}
                className="text-muted-foreground"
              >
                Skip for now
              </Button>
            </div>

            <div>
              {currentStep < onboardingSteps.length ? (
                <Button onClick={handleNext} disabled={completing}>
                  Next
                </Button>
              ) : (
                <Button
                  onClick={handleComplete}
                  disabled={completing}
                  className="bg-gradient-to-r from-purple-600 to-amber-500 hover:from-purple-700 hover:to-amber-600"
                >
                  {completing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Completing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Complete Setup
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Welcome message for business owners */}
          {currentStep === 1 && (
            <div className="mt-8 p-4 bg-gradient-to-r from-purple-100 to-amber-100 dark:from-purple-900/20 dark:to-amber-900/20 rounded-lg">
              <h3 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">
                Welcome, {user?.user_metadata?.name || user?.email}!
              </h3>
              <p className="text-purple-700 dark:text-purple-300 text-sm">
                You&apos;re now the owner of your loyalty program. This quick setup will help you get started with engaging your customers and growing your business.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
