'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { Loader2, Copy, Check, AlertCircle, Building2, Eye, EyeOff } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

// Define invitation details type
interface InvitationDetails {
  id: string
  email: string
  ownerName: string
  phoneNumber?: string
  companyName: string
  companySlug: string
  invitedBy: string
  expiresAt: string
  tempPassword: string
}

// Define account creation result type
interface AccountCreationResult {
  email: string
  name: string
  role: string
  companyName: string
  companySlug: string
}

// Main component that uses Suspense for useSearchParams
export default function AcceptBusinessOwnerInvitationPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center gap-2 justify-center">
              <Building2 className="h-5 w-5" />
              Loading Invitation
            </CardTitle>
            <CardDescription>Please wait while we verify your business owner invitation...</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    }>
      <InvitationContent />
    </Suspense>
  )
}

// Content component that uses useSearchParams safely inside Suspense
function InvitationContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')

  const [loading, setLoading] = useState(false)
  const [invitationDetails, setInvitationDetails] = useState<InvitationDetails | null>(null)
  const [invitationLoading, setInvitationLoading] = useState(true)
  const [invitationError, setInvitationError] = useState<string | null>(null)

  const [accountResult, setAccountResult] = useState<AccountCreationResult | null>(null)
  const [copied, setCopied] = useState(false)
  const [showTempPassword, setShowTempPassword] = useState(false)

  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [passwordErrors, setPasswordErrors] = useState<{ password?: string; confirmPassword?: string }>({})

  // Fetch invitation details
  useEffect(() => {
    if (!token) {
      setInvitationLoading(false)
      setInvitationError('No invitation token provided')
      return
    }

    fetch(`/api/business-owners/accept?token=${token}`)
      .then(res => res.json())
      .then(data => {
        if (data.error) {
          setInvitationError(data.error)
        } else {
          setInvitationDetails(data.invitation)
          // Auto-fill password with temp password
          setPassword(data.invitation.tempPassword)
          setConfirmPassword(data.invitation.tempPassword)
        }
      })
      .catch(err => {
        console.error('Error fetching invitation:', err)
        setInvitationError('Failed to load invitation details')
      })
      .finally(() => {
        setInvitationLoading(false)
      })
  }, [token])

  const validatePassword = (pwd: string) => {
    const errors: { password?: string } = {}

    if (pwd.length < 6) {
      errors.password = 'Password must be at least 6 characters long'
    }

    return errors
  }

  const handleCreateAccount = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate passwords
    const pwdErrors = validatePassword(password)
    const confirmErrors: { confirmPassword?: string } = {}

    if (password !== confirmPassword) {
      confirmErrors.confirmPassword = 'Passwords do not match'
    }

    const allErrors = { ...pwdErrors, ...confirmErrors }
    setPasswordErrors(allErrors)

    if (Object.keys(allErrors).length > 0) {
      return
    }

    setLoading(true)

    try {
      // First, accept the invitation and update the user account
      const response = await fetch('/api/business-owners/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create account')
      }

      // Set account result for success display
      setAccountResult({
        email: data.user.email,
        name: data.user.name,
        role: data.user.role,
        companyName: data.company.name,
        companySlug: data.company.slug
      })

      const successMessage = data.user.isExistingUser
        ? '🎉 Business owner account updated successfully!'
        : '🎉 Business owner account created successfully!'

      // Success! Now handle authentication
      toast.success(successMessage)

      // Use the session link if available (more reliable than immediate password auth)
      if (data.sessionLink) {
        console.log('🔗 Using magic link for authentication')
        toast.success('Redirecting to your dashboard...')
        setTimeout(() => {
          window.location.href = data.sessionLink
        }, 1500)
        return
      }

      // Fallback: Try auto-login with password
      try {
        const { createClient } = await import('@/lib/supabase/client')
        const supabase = createClient()

        console.log('🔐 Attempting auto-login with:', {
          email: data.user.email,
          passwordLength: password.length
        })

        // Add a small delay to ensure user creation is fully processed
        await new Promise(resolve => setTimeout(resolve, 1000))

        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: data.user.email,
          password: password,
        })

        if (signInError) {
          console.error('Auto-login failed with error:', signInError)
          console.error('Error details:', {
            message: signInError.message,
            status: signInError.status,
            name: signInError.name
          })

          // If auto-login fails, redirect to login page with success message
          toast.success('Account setup complete! Please log in with your new password.')
          setTimeout(() => {
            router.push(`/login?message=account-created&email=${encodeURIComponent(data.user.email)}`)
          }, 2000)
          return
        }

        console.log('✅ Auto-login successful:', signInData.user?.email)

        // Successful authentication - redirect based on onboarding status
        const redirectPath = data.nextStep === 'onboarding'
          ? `/onboarding?step=${data.company.setup_wizard_step}`
          : '/dashboard'

        setTimeout(() => {
          router.push(redirectPath)
        }, 2000)

      } catch (authError) {
        console.error('Authentication error:', authError)
        // Fallback to login page
        toast.success('Account setup complete! Please log in with your new password.')
        setTimeout(() => {
          router.push(`/login?message=account-created&email=${encodeURIComponent(data.user.email)}`)
        }, 2000)
      }

    } catch (error) {
      console.error('Error creating account:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create account')
    } finally {
      setLoading(false)
    }
  }

  const copyTempPassword = async () => {
    if (!invitationDetails?.tempPassword) return

    try {
      await navigator.clipboard.writeText(invitationDetails.tempPassword)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast.success('Temporary password copied!')
    } catch {
      toast.error('Failed to copy password')
    }
  }

  const useTempPassword = () => {
    if (!invitationDetails?.tempPassword) return

    setPassword(invitationDetails.tempPassword)
    setConfirmPassword(invitationDetails.tempPassword)
    setPasswordErrors({})
    toast.success('Temporary password applied!')
  }

  // Loading state
  if (invitationLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center gap-2 justify-center">
              <Building2 className="h-5 w-5" />
              Loading Invitation
            </CardTitle>
            <CardDescription>Verifying your business owner invitation...</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    )
  }

  // Error state
  if (invitationError || !invitationDetails) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center gap-2 justify-center text-destructive">
              <AlertCircle className="h-5 w-5" />
              Invitation Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Invalid Invitation</AlertTitle>
              <AlertDescription>
                {invitationError || 'This invitation link is invalid or has expired.'}
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/')} className="w-full">
              Return to Home
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  // Success state
  if (accountResult) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center gap-2 justify-center text-green-600">
              <Check className="h-5 w-5" />
              Account Created Successfully!
            </CardTitle>
            <CardDescription>Welcome to Loyal ET</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert className="border-green-200 bg-green-50">
              <Check className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Welcome {accountResult.name}!</AlertTitle>
              <AlertDescription className="text-green-700">
                Your business owner account for <strong>{accountResult.companyName}</strong> has been created successfully.
              </AlertDescription>
            </Alert>

            <div className="space-y-2 p-4 bg-muted rounded-lg">
              <div className="text-sm">
                <strong>Email:</strong> {accountResult.email}
              </div>
              <div className="text-sm">
                <strong>Role:</strong> {accountResult.role}
              </div>
              <div className="text-sm">
                <strong>Company:</strong> {accountResult.companyName}
              </div>
            </div>

            <p className="text-sm text-muted-foreground text-center">
              You will be redirected to the dashboard in a few seconds...
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/dashboard')} className="w-full">
              Go to Dashboard
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  // Main invitation acceptance form
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center gap-2 justify-center">
            <Building2 className="h-5 w-5" />
            Accept Business Owner Invitation
          </CardTitle>
          <CardDescription>
            Create your account to manage <strong>{invitationDetails.companyName}</strong>
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Invitation Details */}
          <div className="space-y-4 p-4 bg-muted rounded-lg">
            <div className="text-center">
              <h3 className="font-semibold">Welcome {invitationDetails.ownerName}!</h3>
              <p className="text-sm text-muted-foreground">
                You&apos;ve been invited to manage <strong>{invitationDetails.companyName}</strong>
              </p>
            </div>

            <div className="space-y-2 text-sm">
              <div><strong>Email:</strong> {invitationDetails.email}</div>
              {invitationDetails.phoneNumber && (
                <div><strong>Phone:</strong> {invitationDetails.phoneNumber}</div>
              )}
              <div><strong>Invited by:</strong> {invitationDetails.invitedBy}</div>
            </div>
          </div>

          {/* Temporary Password Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Temporary Password</CardTitle>
              <CardDescription>You can use this temporary password or create a new one</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                <code className="flex-1 text-sm font-mono">
                  {showTempPassword ? invitationDetails.tempPassword : '••••••••••••'}
                </code>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowTempPassword(!showTempPassword)}
                  className="shrink-0"
                >
                  {showTempPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={copyTempPassword}
                  className="shrink-0"
                >
                  {copied ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={useTempPassword}
                className="w-full"
              >
                Use Temporary Password
              </Button>
            </CardContent>
          </Card>

          {/* Account Creation Form */}
          <form onSubmit={handleCreateAccount} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value)
                  setPasswordErrors(prev => ({ ...prev, password: undefined }))
                }}
                disabled={loading}
                className={passwordErrors.password ? 'border-destructive' : ''}
              />
              {passwordErrors.password && (
                <p className="text-sm text-destructive">{passwordErrors.password}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="Confirm your password"
                value={confirmPassword}
                onChange={(e) => {
                  setConfirmPassword(e.target.value)
                  setPasswordErrors(prev => ({ ...prev, confirmPassword: undefined }))
                }}
                disabled={loading}
                className={passwordErrors.confirmPassword ? 'border-destructive' : ''}
              />
              {passwordErrors.confirmPassword && (
                <p className="text-sm text-destructive">{passwordErrors.confirmPassword}</p>
              )}
            </div>

            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                'Create Business Owner Account'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
