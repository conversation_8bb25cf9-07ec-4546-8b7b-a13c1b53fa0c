"use client";

import { useState } from "react";
import {
  But<PERSON>
} from "@/components/ui/button";
import { Pencil1Icon, TrashIcon, EyeOpenIcon, StarFilledIcon, StarIcon, PlusIcon } from "@radix-ui/react-icons";
import { Diamond, Trophy } from "lucide-react";
import { useTiers } from "../hooks/use-tiers";
import type { TierDefinition } from "@/app/tiers/types";
import { EditTierDialog } from "@/app/tiers/components/edit-tier-dialog";
import { DeleteConfirmDialog } from "@/components/delete-confirm-dialog";
import { Card, CardContent,CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/query-config";
import { toast } from "sonner";
import { useCompany } from "@/contexts/company-context";

export function TiersTable() {
  const { data: tiers, isLoading, error } = useTiers();
  const [editingTier, setEditingTier] = useState<TierDefinition | null>(null);
  const [deletingTier, setDeletingTier] = useState<TierDefinition | null>(null);
  const queryClient = useQueryClient();
  const { company } = useCompany();

  return (
    <Card className="card mb-8">
      <CardHeader>
        <CardTitle className="text-white">Manage Loyalty Tiers</CardTitle>
        {/* <CardDescription>
          Define and manage your loyalty program tiers and benefits
        </CardDescription> */}
      </CardHeader>
      <CardContent>

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-shimmer w-full h-12 rounded-lg mb-2"></div>
            <div className="animate-shimmer w-full h-12 rounded-lg mb-2"></div>
            <div className="animate-shimmer w-full h-12 rounded-lg"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-500">
            Error loading tiers: {error.message}
          </div>
        ) : tiers && tiers.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {tiers.map((tier: TierDefinition) => (
              <div key={tier.id} className="border rounded-lg overflow-hidden bg-card shadow-sm">
                {/* Card Header with Tier Badge */}
                <div className={`h-40 flex flex-col items-center justify-center ${getTierBackgroundClass(tier.tier_name)}`}>
                  <div className={`text-2xl font-bold mb-2 ${getTierTextColor(tier.tier_name)}`}>
                    {tier.tier_name === "Gold" && <StarFilledIcon className="h-6 w-6 inline mr-2 text-amber-600 dark:text-amber-300" />}
                    {tier.tier_name === "Silver" && <StarIcon className="h-6 w-6 inline mr-2 text-slate-600 dark:text-slate-300" />}
                    {tier.tier_name === "Platinum" && <Diamond className="h-6 w-6 inline mr-2 text-indigo-600 dark:text-indigo-300" />}
                    {tier.tier_name}
                  </div>
                  <div className={`px-4 py-1.5 rounded-full border shadow-sm text-sm font-medium ${getTierBadgeStyle(tier.tier_name)}`}>
                    {tier.minimum_points === 0 ? "Basic" : tier.minimum_points >= 2000 ? "Premium" : "Standard"}
                  </div>
                </div>

                {/* Card Content */}
                <div className="p-6">
                  <div className="mb-4">
                    <div className="flex flex-col mb-4">
                      <div className="flex items-baseline">
                        <span className="text-3xl font-bold text-primary">{tier.minimum_points.toLocaleString()}</span>
                        <span className="ml-2 text-sm font-normal text-muted-foreground">points minimum</span>
                      </div>
                      <div className="h-1 w-full bg-muted/50 rounded-full mt-2 overflow-hidden">
                        <div className={`h-full ${getTierBackgroundClass(tier.tier_name)}`} style={{
                          width: `${Math.min(100, tier.minimum_points / 25)}%`
                        }}></div>
                      </div>
                    </div>

                    <p className="text-sm text-foreground mb-4 line-clamp-3">
                      {tier.benefits_description}
                    </p>
                  </div>

                  <div className="flex justify-between items-center mb-5 bg-muted/20 p-3 rounded-lg">
                    <div className="flex flex-col">
                      <span className="text-sm text-muted-foreground">Members</span>
                      <span className="text-lg font-medium">{tier.member_count || 0}</span>
                    </div>
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getTierBackgroundClass(tier.tier_name)}`}>
                      <span className={`text-sm font-bold ${getTierTextColor(tier.tier_name)}`}>{tier.tier_name.charAt(0)}</span>
                    </div>
                  </div>

                  <div className="flex gap-2 justify-end border-t pt-4 mt-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/tiers/${tier.id}`}>
                        <EyeOpenIcon className="h-4 w-4 mr-1" />
                        View
                      </Link>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingTier(tier)}
                    >
                      <Pencil1Icon className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setDeletingTier(tier)}
                    >
                      <TrashIcon className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 space-y-4">
            <p>No tiers found. Create your first tier or generate default tiers to get started!</p>
            <div className="flex justify-center gap-4">
              <Button 
                variant="default" 
                className="bg-primary hover:bg-primary/90"
                onClick={async () => {
                  try {
                    const response = await fetch(`/api/companies/create-default-tiers`, {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify({ companyId: company?.id }),
                    });
                    
                    if (!response.ok) {
                      throw new Error('Failed to create default tiers');
                    }
                    
                    // Refetch tiers after creating defaults
                    queryClient.invalidateQueries({ queryKey: queryKeys.tiers(company?.id || '') });
                    toast.success('Default tiers created successfully!');
                  } catch (error) {
                    console.error('Error creating default tiers:', error);
                    toast.error('Failed to create default tiers');
                  }
                }}
              >
                <Trophy className="h-4 w-4 mr-2" />
                Generate Default Tiers
              </Button>
              <Button asChild variant="outline">
                <Link href="/tiers/new">
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create Custom Tier
                </Link>
              </Button>
            </div>
          </div>
        )}
      </CardContent>

      {/* Dialogs */}
      {editingTier && (
        <EditTierDialog
          tier={editingTier}
          open={!!editingTier}
          onOpenChange={() => setEditingTier(null)}
        />
      )}

      {deletingTier && (
        <DeleteConfirmDialog
          title="Delete Tier"
          description={`Are you sure you want to delete the "${deletingTier.tier_name}" tier? This action cannot be undone.`}
          open={!!deletingTier}
          onOpenChange={() => setDeletingTier(null)}
          onConfirm={async () => {
            try {
              const response = await fetch(`/api/tiers/${deletingTier.id}`, {
                method: 'DELETE',
              });
              
              if (!response.ok) {
                throw new Error('Failed to delete tier');
              }
              
              // Refetch tiers after deletion
              queryClient.invalidateQueries({ queryKey: queryKeys.tiers(company?.id || '') });
              toast.success(`${deletingTier.tier_name} tier deleted successfully!`);
            } catch (error) {
              console.error('Error deleting tier:', error);
              toast.error('Failed to delete tier');
            } finally {
              setDeletingTier(null);
            }
          }}
        />
      )}
    </Card>
  );
}

// Helper function to determine card background class based on tier name
function getTierBackgroundClass(tierName: string): string {
  const name = tierName.toLowerCase();
  if (name === "gold") return "bg-gradient-to-br from-yellow-300 to-amber-500 dark:from-yellow-500/60 dark:to-amber-700/60";
  if (name === "silver") return "bg-gradient-to-br from-gray-300 to-slate-400 dark:from-gray-400/60 dark:to-slate-600/60";
  if (name === "platinum") return "bg-gradient-to-br from-indigo-300 via-purple-300 to-pink-300 dark:from-indigo-600/60 dark:via-purple-600/50 dark:to-pink-600/60";
  return "bg-muted";
}

// Helper function to get tier text color
function getTierTextColor(tierName: string): string {
  const name = tierName.toLowerCase();
  if (name === "gold") return "text-amber-800 dark:text-amber-100";
  if (name === "silver") return "text-slate-800 dark:text-slate-100";
  if (name === "platinum") return "text-indigo-800 dark:text-indigo-100";
  return "text-foreground";
}

// Helper function to get tier badge style
function getTierBadgeStyle(tierName: string): string {
  const name = tierName.toLowerCase();
  if (name === "gold") return "bg-amber-100 text-amber-800 dark:bg-amber-900/80 dark:text-amber-100 border-amber-200 dark:border-amber-700";
  if (name === "silver") return "bg-slate-100 text-slate-800 dark:bg-slate-800/80 dark:text-slate-100 border-slate-200 dark:border-slate-700";
  if (name === "platinum") return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/80 dark:text-indigo-100 border-indigo-200 dark:border-indigo-700";
  return "";
}
