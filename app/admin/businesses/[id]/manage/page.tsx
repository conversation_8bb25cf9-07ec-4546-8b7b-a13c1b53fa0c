'use client'

import { use } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import { Building2, ArrowLeft, Bot, Users, CreditCard, Gift } from 'lucide-react'
import { useSuperAdmin } from '@/hooks/use-super-admin'
import { useBusinessDetailsWithStates } from '@/hooks/use-business-details'
import { BusinessDetailsSkeletonStaggered } from '@/components/business-details-skeleton'
import { ErrorDisplay } from '@/components/error-display'

export default function BusinessManagePage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter()
  const resolvedParams = use(params)
  const businessId = resolvedParams.id

  // Check super admin status
  const { isSuperAdmin, isLoading: superAdminLoading } = useSuperAdmin()

  // Use React Query hook for data fetching
  const {
    businessDetails,
    loading,
    error,
    refetch
  } = useBusinessDetailsWithStates(businessId)

  // Show loading while checking super admin status or loading business data
  if (superAdminLoading || loading) {
    return <BusinessDetailsSkeletonStaggered />
  }

  // Redirect if not super admin
  if (!isSuperAdmin) {
    toast.error('Access denied - Super admin privileges required')
    router.push('/dashboard')
    return null
  }

  // Show error if there's an issue
  if (error) {
    return (
      <ErrorDisplay
        error={error}
        onRetry={() => refetch()}
        onBack={() => router.push('/admin/businesses')}
        context="loading business details"
        showTechnicalDetails={false}
      />
    )
  }

  // Show message if no business found
  if (!businessDetails) {
    return (
      <ErrorDisplay
        error={new Error('Business not found or access denied')}
        onRetry={() => refetch()}
        onBack={() => router.push('/admin/businesses')}
        context="accessing business data"
      />
    )
  }

  // Get business and stats from businessDetails
  const business = businessDetails?.business
  const stats = businessDetails?.stats || { members: 0, transactions: 0, rewards: 0 }

  // Additional safety check
  if (!business) {
    return (
      <ErrorDisplay
        error={new Error('Business data is not available')}
        onRetry={() => refetch()}
        onBack={() => router.push('/admin/businesses')}
        context="loading business information"
      />
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.push('/admin/businesses')} size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Building2 className="h-6 w-6" />
              {business.name}
            </h1>
            <p className="text-muted-foreground">Business Management Dashboard</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant={business.is_active ? "default" : "secondary"}>
            {business.is_active ? 'Active' : 'Inactive'}
          </Badge>
          <Badge variant={business.bot_tier === 'premium' ? "default" : "secondary"}>
            {business.bot_tier === 'premium' ? 'Premium Bot' : 'Standard Bot'}
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Users className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">{stats.members}</p>
              <p className="text-sm text-muted-foreground">Members</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <CreditCard className="h-8 w-8 text-green-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">{stats.transactions}</p>
              <p className="text-sm text-muted-foreground">Transactions</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-6">
            <Gift className="h-8 w-8 text-purple-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">{stats.rewards}</p>
              <p className="text-sm text-muted-foreground">Rewards</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Business Overview</CardTitle>
              <CardDescription>
                Key information and performance metrics for {business.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <p className="font-medium">Business ID</p>
                  <p className="text-muted-foreground font-mono text-xs">{business.id}</p>
                </div>
                <div>
                  <p className="font-medium">Slug</p>
                  <p className="text-muted-foreground">{business.slug}</p>
                </div>
                <div>
                  <p className="font-medium">Business Type</p>
                  <p className="text-muted-foreground">{business.business_type || 'Not specified'}</p>
                </div>
                <div>
                  <p className="font-medium">Points Ratio</p>
                  <p className="text-muted-foreground">{business.points_earning_ratio}:1</p>
                </div>
                <div>
                  <p className="font-medium">Points Expiry</p>
                  <p className="text-muted-foreground">{business.points_expiration_days} days</p>
                </div>
                <div>
                  <p className="font-medium">Created</p>
                  <p className="text-muted-foreground">
                    {new Date(business.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {business.bot_configurations && (
                <div className="mt-6 p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    <Bot className="h-5 w-5" />
                    <h3 className="font-semibold">Bot Configuration</h3>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <p className="font-medium">Bot Username</p>
                      <p className="text-muted-foreground">@{business.bot_configurations.bot_username}</p>
                    </div>
                    <div>
                      <p className="font-medium">Status</p>
                      <Badge variant={business.bot_configurations.is_active ? "default" : "secondary"}>
                        {business.bot_configurations.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                    <div>
                      <p className="font-medium">Messages</p>
                      <p className="text-muted-foreground">{business.bot_configurations.message_count}</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Members Management</CardTitle>
              <CardDescription>
                View and manage loyalty program members
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Members management functionality will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transactions History</CardTitle>
              <CardDescription>
                View and manage transaction records
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Transactions history functionality will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Business Settings</CardTitle>
              <CardDescription>
                Configure business settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Business settings functionality will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
