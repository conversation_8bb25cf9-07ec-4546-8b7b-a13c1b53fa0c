'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { Plus, Building2, Bot, Search, RefreshCw, Eye, Settings, UserPlus, Copy, CheckCircle, MessageCircle, Send } from 'lucide-react'
import { useSuperAdmin } from '@/hooks/use-super-admin'
import { useRouter } from 'next/navigation'
import { useBusinesses, useCreateBusiness } from '@/hooks/use-businesses'
import { BusinessDetailsDialog } from '@/components/business-details-dialog'
import { BusinessOwnerInvite } from '@/components/business-owner-invite'
import { BusinessOwnerInvitationManagement } from '@/components/business-owner-invitation-management'
import { BusinessListSkeleton } from '@/components/business-list-skeleton'
import { InlineErrorDisplay } from '@/components/error-display'

import type { Business, CreateBusinessForm } from '@/hooks/use-businesses'

export default function AdminBusinessesPage() {
  const { isSuperAdmin, isLoading: superAdminLoading } = useSuperAdmin()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [selectedBusinessId, setSelectedBusinessId] = useState<string | null>(null)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [copiedLink, setCopiedLink] = useState(false)
  const [copiedPassword, setCopiedPassword] = useState(false)

  // Use React Query hooks for data fetching (search is handled in the hook)
  const { data: businesses = [], isLoading: loading, error, refetch } = useBusinesses(searchTerm)
  const createBusinessMutation = useCreateBusiness()

  const [form, setForm] = useState<CreateBusinessForm>({
    businessName: '',
    businessEmail: '',
    businessPhone: '',
    businessAddress: '',
    ownerName: '',
    ownerEmail: '',
    ownerPhone: '',
    botTier: 'premium',
    botToken: '',
    pointsEarningRatio: 1.0,
    currency: 'ETB',
    brandingConfig: {
      welcomeMessage: '',
      primaryColor: '#3B82F6',
      logoUrl: ''
    }
  })
  interface CreatedBusinessData {
    company: {
      id: string
      name: string
      slug: string
      bot_tier: string
    }
    owner: {
      id: string
      email: string
      name: string
      tempPassword: string
    }
    invitation: {
      id: string
      token: string
      invitationLink: string
      telegramLink: string | null
      expiresAt: string
    } | null
  }

  const [createdBusinessData, setCreatedBusinessData] = useState<CreatedBusinessData | null>(null)

  const handleCreateBusiness = async () => {
    // Use the mutation to create business
    createBusinessMutation.mutate(form, {
      onSuccess: (result) => {
        setCreatedBusinessData(result.data)
        setShowCreateDialog(false)
        setShowSuccessDialog(true)
        // Reset form
        setForm({
          businessName: '',
          businessEmail: '',
          businessPhone: '',
          businessAddress: '',
          ownerName: '',
          ownerEmail: '',
          ownerPhone: '',
          botTier: 'premium',
          botToken: '',
          pointsEarningRatio: 1.0,
          currency: 'ETB',
          brandingConfig: {
            welcomeMessage: '',
            primaryColor: '#3B82F6',
            logoUrl: ''
          }
        })
      }
    })
  }

  const copyToClipboard = async (text: string, type: 'link' | 'password') => {
    try {
      await navigator.clipboard.writeText(text)
      if (type === 'link') {
        setCopiedLink(true)
        setTimeout(() => setCopiedLink(false), 2000)
        toast.success('Invitation link copied!')
      } else {
        setCopiedPassword(true)
        setTimeout(() => setCopiedPassword(false), 2000)
        toast.success('Password copied!')
      }
    } catch {
      toast.error('Failed to copy to clipboard')
    }
  }

  const shareViaWhatsApp = () => {
    if (!createdBusinessData?.invitation?.invitationLink) return
    
    const message = `Hi ${createdBusinessData.owner.name}! 👋\n\nYou've been invited to manage your business account at ${createdBusinessData.company.name} on the Loyal ET platform!\n\nClick this link to get started:\n${createdBusinessData.invitation.invitationLink}\n\nTemporary Password: ${createdBusinessData.owner.tempPassword}\n\n${createdBusinessData.invitation.telegramLink ? `📱 You can also join via Telegram: ${createdBusinessData.invitation.telegramLink}\n\n` : ''}Welcome to Loyal ET! 🎉`

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  const shareViaSMS = () => {
    if (!createdBusinessData?.invitation?.invitationLink) return

    const message = `Hi ${createdBusinessData.owner.name}! You've been invited to manage ${createdBusinessData.company.name} on Loyal ET. Link: ${createdBusinessData.invitation.invitationLink} Password: ${createdBusinessData.owner.tempPassword}`
    const smsUrl = `sms:?body=${encodeURIComponent(message)}`
    window.open(smsUrl)
  }

  // Redirect if not super admin
  useEffect(() => {
    if (!superAdminLoading && !isSuperAdmin) {
      toast.error('Access denied - Super admin privileges required')
      router.push('/dashboard')
    }
  }, [isSuperAdmin, superAdminLoading, router])

  // Show loading while checking super admin status
  if (superAdminLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Verifying admin privileges...</p>
        </div>
      </div>
    )
  }

  // Don't render anything if not super admin
  if (!isSuperAdmin) {
    return null
  }

  const getBotStatusBadge = (business: Business) => {
    if (business.bot_tier === 'standard') {
      return <Badge variant="secondary">Standard Bot</Badge>
    }

    if (business.bot_configuration_id) {
      return <Badge variant="default">Premium Bot (Configured)</Badge>
    }

    return <Badge variant="outline">Premium (Not Configured)</Badge>
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Business Management</h1>
          <p className="text-muted-foreground">Manage businesses and their Telegram bot integrations</p>
        </div>
      </div>

      <Tabs defaultValue="businesses" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="businesses" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Businesses ({businesses.length})
          </TabsTrigger>
          <TabsTrigger value="invitations" className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Owner Invitations
          </TabsTrigger>
        </TabsList>

        <TabsContent value="businesses" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search businesses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Button variant="outline" onClick={() => refetch()} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Business
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Business</DialogTitle>
              <DialogDescription>
                Set up a new business with premium Telegram bot integration
              </DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="business" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="business">Business Info</TabsTrigger>
                <TabsTrigger value="owner">Owner Details</TabsTrigger>
                <TabsTrigger value="bot">Bot Configuration</TabsTrigger>
                <TabsTrigger value="branding">Branding</TabsTrigger>
              </TabsList>

              <TabsContent value="business" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="businessName">Business Name *</Label>
                    <Input
                      id="businessName"
                      value={form.businessName}
                      onChange={(e) => setForm({ ...form, businessName: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessEmail">Business Email *</Label>
                    <Input
                      id="businessEmail"
                      type="email"
                      value={form.businessEmail}
                      onChange={(e) => setForm({ ...form, businessEmail: e.target.value })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="businessPhone">Business Phone *</Label>
                    <Input
                      id="businessPhone"
                      value={form.businessPhone}
                      onChange={(e) => setForm({ ...form, businessPhone: e.target.value })}
                      placeholder="+251911234567"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Currency</Label>
                    <Select value={form.currency} onValueChange={(value) => setForm({ ...form, currency: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ETB">Ethiopian Birr (ETB)</SelectItem>
                        <SelectItem value="USD">US Dollar (USD)</SelectItem>
                        <SelectItem value="EUR">Euro (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessAddress">Business Address</Label>
                  <Textarea
                    id="businessAddress"
                    value={form.businessAddress}
                    onChange={(e) => setForm({ ...form, businessAddress: e.target.value })}
                    placeholder="Enter business address"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pointsRatio">Points Earning Ratio</Label>
                  <Input
                    id="pointsRatio"
                    type="number"
                    step="0.1"
                    min="0.1"
                    max="100"
                    value={form.pointsEarningRatio}
                    onChange={(e) => setForm({ ...form, pointsEarningRatio: parseFloat(e.target.value) || 1.0 })}
                    placeholder="1.0 (1 point per 1 ETB)"
                  />
                </div>
              </TabsContent>

              <TabsContent value="owner" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ownerName">Owner Name *</Label>
                    <Input
                      id="ownerName"
                      value={form.ownerName}
                      onChange={(e) => setForm({ ...form, ownerName: e.target.value })}
                      placeholder="Enter owner full name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ownerEmail">Owner Email *</Label>
                    <Input
                      id="ownerEmail"
                      type="email"
                      value={form.ownerEmail}
                      onChange={(e) => setForm({ ...form, ownerEmail: e.target.value })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ownerPhone">Owner Phone *</Label>
                  <Input
                    id="ownerPhone"
                    value={form.ownerPhone}
                    onChange={(e) => setForm({ ...form, ownerPhone: e.target.value })}
                    placeholder="+251911234567"
                  />
                </div>
              </TabsContent>

              <TabsContent value="bot" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="botTier">Bot Tier</Label>
                  <Select value={form.botTier} onValueChange={(value: 'standard' | 'premium') => setForm({ ...form, botTier: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">Standard (Shared Bot)</SelectItem>
                      <SelectItem value="premium">Premium (Dedicated Bot)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {form.botTier === 'premium' && (
                  <div className="space-y-2">
                    <Label htmlFor="botToken">Bot Token *</Label>
                    <Input
                      id="botToken"
                      type="password"
                      value={form.botToken}
                      onChange={(e) => setForm({ ...form, botToken: e.target.value })}
                      placeholder="Enter bot token from BotFather"
                    />
                    <p className="text-sm text-muted-foreground">
                      Create a new bot via @BotFather on Telegram and paste the token here
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="branding" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="welcomeMessage">Welcome Message</Label>
                  <Textarea
                    id="welcomeMessage"
                    value={form.brandingConfig.welcomeMessage}
                    onChange={(e) => setForm({
                      ...form,
                      brandingConfig: { ...form.brandingConfig, welcomeMessage: e.target.value }
                    })}
                    placeholder="Custom welcome message for the bot"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <Input
                      id="primaryColor"
                      type="color"
                      value={form.brandingConfig.primaryColor}
                      onChange={(e) => setForm({
                        ...form,
                        brandingConfig: { ...form.brandingConfig, primaryColor: e.target.value }
                      })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="logoUrl">Logo URL</Label>
                    <Input
                      id="logoUrl"
                      type="url"
                      value={form.brandingConfig.logoUrl}
                      onChange={(e) => setForm({
                        ...form,
                        brandingConfig: { ...form.brandingConfig, logoUrl: e.target.value }
                      })}
                      placeholder="https://example.com/logo.png"
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateBusiness} disabled={createBusinessMutation.isPending}>
                {createBusinessMutation.isPending ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create Business'
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

          <div className="grid gap-4">
        {error && (
          <InlineErrorDisplay
            error={error}
            onRetry={() => refetch()}
            context="loading businesses"
          />
        )}
        {loading ? (
          <BusinessListSkeleton count={6} />
        ) : businesses.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Building2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">No businesses found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ? 'No businesses match your search criteria' : 'Get started by creating your first business'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Business
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          businesses.map((business: Business) => (
            <Card key={business.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      {business.name}
                    </CardTitle>
                    <CardDescription>ID: {business.id}</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    {getBotStatusBadge(business)}
                    <Badge variant={business.is_active ? "default" : "secondary"}>
                      {business.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="font-medium">Business Type</p>
                    <p className="text-muted-foreground">{business.business_type || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="font-medium">Points Ratio</p>
                    <p className="text-muted-foreground">{business.points_earning_ratio || 1.0}:1</p>
                  </div>
                  <div>
                    <p className="font-medium">Created</p>
                    <p className="text-muted-foreground">
                      {new Date(business.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className="font-medium">Onboarding</p>
                    <p className="text-muted-foreground">
                      {business.onboarding_completed ? 'Complete' : 'Pending'}
                    </p>
                  </div>
                </div>

                {business.bot_configurations && (
                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Bot className="h-4 w-4" />
                      <span className="font-medium">Bot: @{business.bot_configurations.bot_username}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="font-medium">Messages</p>
                        <p className="text-muted-foreground">{business.bot_configurations.message_count || 0}</p>
                      </div>
                      <div>
                        <p className="font-medium">Last Activity</p>
                        <p className="text-muted-foreground">
                          {business.bot_configurations.last_activity
                            ? new Date(business.bot_configurations.last_activity).toLocaleString()
                            : 'Never'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex justify-end gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedBusinessId(business.id)
                      setShowDetailsDialog(true)
                    }}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                  <BusinessOwnerInvite
                    companyId={business.id}
                    companyName={business.name}
                    trigger={
                      <Button variant="outline" size="sm">
                        <UserPlus className="h-4 w-4 mr-2" />
                        Invite Owner
                      </Button>
                    }
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Redirect to business management page with the specific business ID
                      router.push(`/admin/businesses/${business.id}/manage`)
                    }}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Manage
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
          </div>
        </TabsContent>

        <TabsContent value="invitations">
          <BusinessOwnerInvitationManagement />
        </TabsContent>
      </Tabs>

      {/* Business Details Dialog */}
      {selectedBusinessId && (
        <BusinessDetailsDialog
          businessId={selectedBusinessId}
          isOpen={showDetailsDialog}
          onClose={() => {
            setShowDetailsDialog(false)
            setSelectedBusinessId(null)
          }}
        />
      )}

      {/* Success Dialog for Business Creation */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Business Created Successfully!
            </DialogTitle>
            <DialogDescription>
              The business has been created and an invitation has been sent to the owner.
            </DialogDescription>
          </DialogHeader>

          {createdBusinessData && (
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <h3 className="text-lg font-semibold text-green-700">
                  {createdBusinessData.company.name}
                </h3>
                <p className="text-sm text-muted-foreground">
                  Owner: <strong>{createdBusinessData.owner.name}</strong> ({createdBusinessData.owner.email})
                </p>
              </div>

              {/* Invitation Link */}
              {createdBusinessData.invitation && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Invitation Link</CardTitle>
                    <CardDescription>Share this link with the business owner</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                      <code className="flex-1 text-sm font-mono break-all">
                        {createdBusinessData.invitation.invitationLink}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => copyToClipboard(createdBusinessData.invitation!.invitationLink, 'link')}
                        className="shrink-0"
                      >
                        {copiedLink ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Temporary Password */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Temporary Password</CardTitle>
                  <CardDescription>Share this password with the business owner</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                    <code className="flex-1 text-sm font-mono">
                      {createdBusinessData.owner.tempPassword}
                    </code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(createdBusinessData.owner.tempPassword, 'password')}
                      className="shrink-0"
                    >
                      {copiedPassword ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Sharing Options */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Share Invitation</CardTitle>
                  <CardDescription>Send the invitation to the business owner</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-3">
                    <Button onClick={shareViaWhatsApp} className="gap-2">
                      <MessageCircle className="h-4 w-4" />
                      WhatsApp
                    </Button>
                    <Button onClick={shareViaSMS} variant="outline" className="gap-2">
                      <Send className="h-4 w-4" />
                      SMS
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end">
                <Button onClick={() => {
                  setShowSuccessDialog(false)
                  setCreatedBusinessData(null)
                  setCopiedLink(false)
                  setCopiedPassword(false)
                }}>
                  Done
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
