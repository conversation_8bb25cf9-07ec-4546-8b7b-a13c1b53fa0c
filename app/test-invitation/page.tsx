'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react'

export default function TestInvitationPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<{
    success: boolean;
    invitation: {
      id: string;
      email: string;
      ownerName: string;
      phoneNumber: string;
      expiresAt: string;
      invitationToken: string;
      invitationLink: string;
      tempPassword: string;
    };
    message: string;
  } | null>(null)
  const [error, setError] = useState<string | null>(null)

  const createTestInvitation = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      // Create the invitation directly using service role
      const response = await fetch('/api/test-create-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          ownerName: 'edf',
          phoneNumber: '+25188888888',
          companyId: '77d7eadb-0eb5-4719-9ac6-6b6d18f5eb16'
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create invitation')
      }

      setResult(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const testInvitationFlow = async () => {
    if (!result?.invitation?.invitationToken) {
      setError('No invitation token available. Create invitation first.')
      return
    }

    try {
      // Test the invitation validation
      const token = result.invitation.invitationToken
      const validateResponse = await fetch(`/api/business-owners/accept?token=${token}`)
      const validateData = await validateResponse.json()

      if (validateResponse.ok) {
        alert('✅ Invitation validation successful! You can now test the acceptance flow.')
        // Open the invitation acceptance page
        window.open(`/business-owners/accept?token=${token}`, '_blank')
      } else {
        setError(`Validation failed: ${validateData.error}`)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Validation test failed')
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Business Owner Invitation Test</CardTitle>
          <CardDescription>
            Test the business owner invitation <NAME_EMAIL>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <div className="space-y-2">
                  <div><strong>Invitation Created Successfully!</strong></div>
                  <div><strong>Email:</strong> {result.invitation.email}</div>
                  <div><strong>Token:</strong> {result.invitation.invitationToken}</div>
                  <div><strong>Temp Password:</strong> {result.invitation.tempPassword}</div>
                  <div><strong>Link:</strong> 
                    <a 
                      href={result.invitation.invitationLink} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline ml-1"
                    >
                      {result.invitation.invitationLink}
                    </a>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            <Button 
              onClick={createTestInvitation} 
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Invitation...
                </>
              ) : (
                'Create Test Invitation'
              )}
            </Button>

            {result && (
              <Button 
                onClick={testInvitationFlow} 
                variant="outline"
                className="w-full"
              >
                Test Invitation Flow
              </Button>
            )}
          </div>

          <div className="text-sm text-muted-foreground space-y-2">
            <div><strong>Test Data:</strong></div>
            <div>Email: <EMAIL></div>
            <div>Company: edf (77d7eadb-0eb5-4719-9ac6-6b6d18f5eb16)</div>
            <div>Owner: edf</div>
            <div>Phone: +25188888888</div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
