import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { CACHE_TIMES, queryKeys, COMMON_QUERY_OPTIONS } from '@/lib/query-config'
import { toast } from 'sonner'

export interface BusinessDetails {
  business: {
    id: string
    name: string
    slug: string
    logo_url?: string
    primary_color?: string
    points_expiration_days: number
    bot_tier: 'standard' | 'premium'
    bot_configuration_id?: string
    is_active: boolean
    created_at: string
    points_earning_ratio: number
    administrator_id: string
    business_type?: string
    onboarding_completed?: boolean
    setup_wizard_step: number
    bot_configurations?: {
      id: string
      bot_token: string
      bot_username: string
      webhook_url: string
      webhook_secret: string
      is_active: boolean
      last_activity?: string
      message_count: number
      created_at: string
      creation_status: string
      last_health_check?: string
    }
  }
  owner: {
    administrator_id: string
    role: string
    created_at: string
    users?: {
      id: string
      email: string
      raw_user_meta_data?: Record<string, unknown>
      created_at: string
      last_sign_in_at?: string
    }
  }
  stats: {
    members: number
    transactions: number
    rewards: number
  }
  recentTransactions?: Array<{
    id: string
    transaction_type: string
    points_earned: number
    created_at: string
    loyalty_members: {
      name: string
      loyalty_id: string
    }
  }>
}

/**
 * Hook for fetching detailed information about a specific business
 * Implements proper caching and error handling following app patterns
 */
export function useBusinessDetails(businessId: string | null) {
  return useQuery({
    queryKey: queryKeys.businessDetails(businessId || ''),
    queryFn: async (): Promise<BusinessDetails> => {
      if (!businessId) {
        throw new Error('Business ID is required')
      }

      console.log('useBusinessDetails: Fetching details for business:', businessId)

      const response = await fetch(`/api/admin/businesses/${businessId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
        }
      })

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Business not found')
        }
        if (response.status === 401 || response.status === 403) {
          throw new Error('Unauthorized access')
        }
        throw new Error(`Failed to fetch business details: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Invalid response format')
      }

      console.log('useBusinessDetails: Retrieved business details successfully')
      return result.data
    },
    enabled: !!businessId, // Only run query if businessId is provided
    staleTime: CACHE_TIMES.MEDIUM, // 5 minutes
    gcTime: CACHE_TIMES.MEDIUM * 2, // 10 minutes
    ...COMMON_QUERY_OPTIONS,
    retry: (failureCount, error) => {
      // Don't retry on auth or not found errors
      const errorStatus = (error as { status?: number })?.status
      if (errorStatus === 401 || errorStatus === 403 || errorStatus === 404) return false
      return failureCount < 2
    }
  })
}

/**
 * Hook for updating business information
 * Implements optimistic updates and cache invalidation
 */
export function useUpdateBusiness() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ businessId, updates }: {
      businessId: string
      updates: Partial<BusinessDetails['business']>
    }): Promise<BusinessDetails['business']> => {
      console.log('useUpdateBusiness: Updating business:', businessId, updates)

      const response = await fetch(`/api/admin/businesses/${businessId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || 'Failed to update business')
      }

      const result = await response.json()

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Invalid response format')
      }

      return result.data
    },
    onSuccess: (updatedBusiness, { businessId }) => {
      console.log('useUpdateBusiness: Business updated successfully')

      toast.success('Business updated successfully!')

      // Invalidate business details cache
      queryClient.invalidateQueries({ queryKey: queryKeys.businessDetails(businessId) })

      // Also invalidate businesses list in case name or other list fields changed
      queryClient.invalidateQueries({ queryKey: queryKeys.businesses() })
    },
    onError: (error) => {
      console.error('useUpdateBusiness: Error updating business:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update business')
    }
  })
}

/**
 * Hook for business statistics only
 * Separate hook for frequently updated stats with shorter cache time
 */
export function useBusinessStats(businessId: string | null) {
  return useQuery({
    queryKey: queryKeys.businessStats(businessId || ''),
    queryFn: async () => {
      if (!businessId) {
        throw new Error('Business ID is required')
      }

      console.log('useBusinessStats: Fetching stats for business:', businessId)

      const businessDetails = await fetch(`/api/admin/businesses/${businessId}`, {
        credentials: 'include'
      }).then(res => {
        if (!res.ok) throw new Error('Failed to fetch business stats')
        return res.json()
      })

      return businessDetails.data?.stats || { members: 0, transactions: 0, rewards: 0 }
    },
    enabled: !!businessId,
    staleTime: CACHE_TIMES.SHORT, // 1 minute - stats change more frequently
    gcTime: CACHE_TIMES.MEDIUM, // 5 minutes
    ...COMMON_QUERY_OPTIONS
  })
}

/**
 * Hook for getting business details with loading and error states
 * Simplified interface for components that need basic business details functionality
 */
export function useBusinessDetailsWithStates(businessId: string | null) {
  const { data: businessDetails, isLoading, error, refetch } = useBusinessDetails(businessId)

  return {
    businessDetails: businessDetails || null,
    loading: isLoading,
    error,
    refetch,
    business: businessDetails?.business || null,
    owner: businessDetails?.owner || null,
    stats: businessDetails?.stats || { members: 0, transactions: 0, rewards: 0 },
    recentTransactions: businessDetails?.recentTransactions || []
  }
}
