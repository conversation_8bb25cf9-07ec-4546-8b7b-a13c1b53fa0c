import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { CACHE_TIMES, queryKeys, COMMON_QUERY_OPTIONS } from '@/lib/query-config'
import { toast } from 'sonner'

export interface Business {
  id: string
  name: string
  slug: string
  bot_tier: 'standard' | 'premium'
  bot_configuration_id?: string
  is_active: boolean
  created_at: string
  points_earning_ratio: number
  administrator_id: string
  business_type?: string
  onboarding_completed?: boolean
  bot_configurations?: {
    id: string
    bot_username: string
    is_active: boolean
    last_activity?: string
    message_count?: number
  }
}

export interface CreateBusinessForm {
  businessName: string
  businessEmail: string
  businessPhone: string
  businessAddress: string
  ownerName: string
  ownerEmail: string
  ownerPhone: string
  botTier: 'standard' | 'premium'
  botToken: string
  pointsEarningRatio: number
  currency: string
  brandingConfig: {
    welcomeMessage: string
    primaryColor: string
    logoUrl: string
  }
}

/**
 * Hook for fetching list of businesses (super admin only)
 * Implements proper caching and error handling following app patterns
 */
export function useBusinesses(searchTerm?: string) {
  return useQuery({
    queryKey: queryKeys.businesses(searchTerm),
    queryFn: async (): Promise<Business[]> => {
      console.log('useBusinesses: Fetching businesses list', searchTerm ? `with search: ${searchTerm}` : '')

      const response = await fetch('/api/admin/businesses/create', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch businesses: ${response.status}`)
      }

      const result = await response.json()
      let businesses = result.data || []

      console.log(`useBusinesses: Retrieved ${businesses.length} businesses`)

      // Apply client-side filtering if search term provided
      // This provides immediate feedback while typing
      if (searchTerm && searchTerm.trim()) {
        businesses = businesses.filter((business: Business) =>
          business.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          business.slug.toLowerCase().includes(searchTerm.toLowerCase())
        )
        console.log(`useBusinesses: Filtered to ${businesses.length} businesses for search: ${searchTerm}`)
      }

      return businesses
    },
    staleTime: CACHE_TIMES.MEDIUM, // 5 minutes - businesses don't change frequently
    gcTime: CACHE_TIMES.MEDIUM * 2, // 10 minutes
    ...COMMON_QUERY_OPTIONS,
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      const errorStatus = (error as { status?: number })?.status
      if (errorStatus === 401 || errorStatus === 403) return false
      return failureCount < 2
    }
  })
}

interface CreateBusinessResponse {
  success: boolean
  data: {
    company: {
      id: string
      name: string
      slug: string
      bot_tier: string
    }
    owner: {
      id: string
      email: string
      name: string
      tempPassword: string
    }
    invitation: {
      id: string
      token: string
      invitationLink: string
      telegramLink: string | null
      expiresAt: string
    } | null
    bot: {
      configId?: string
      tier: string
      status: string
    }
  }
  error?: string
}

/**
 * Hook for creating a new business
 * Implements optimistic updates and proper error handling
 */
export function useCreateBusiness() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (formData: CreateBusinessForm): Promise<CreateBusinessResponse> => {
      console.log('useCreateBusiness: Creating business:', formData.businessName)

      // Validate required fields
      if (!formData.businessName || !formData.businessEmail || !formData.ownerName || !formData.ownerEmail) {
        throw new Error('Please fill in all required fields')
      }

      if (formData.botTier === 'premium' && !formData.botToken) {
        throw new Error('Bot token is required for premium tier')
      }

      const response = await fetch('/api/admin/businesses/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to create business')
      }

      return result
    },
    onSuccess: (result) => {
      console.log('useCreateBusiness: Business created successfully')

      // Show success notification
      toast.success('Business created successfully with owner invitation!')

      // Show credentials and invitation info to admin
      if (result.data?.owner) {
        toast.info(`Owner credentials - Email: ${result.data.owner.email}, Password: ${result.data.owner.tempPassword}`, {
          duration: 10000
        })
      }

      if (result.data?.invitation) {
        toast.success(`Invitation created! Link: ${result.data.invitation.invitationLink}`, {
          duration: 15000
        })
      }

      // Invalidate businesses list to refetch with new business
      queryClient.invalidateQueries({ queryKey: queryKeys.businesses() })
    },
    onError: (error) => {
      console.error('useCreateBusiness: Error creating business:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create business')
    }
  })
}

/**
 * Hook for getting businesses with loading and error states
 * Simplified interface for components that need basic business list functionality
 */
export function useBusinessesWithStates(searchTerm?: string) {
  const { data: businesses, isLoading, error, refetch } = useBusinesses(searchTerm)

  return {
    businesses: businesses || [],
    loading: isLoading,
    error,
    refetch,
    isEmpty: !isLoading && (!businesses || businesses.length === 0)
  }
}
