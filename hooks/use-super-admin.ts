import { useAuth } from './use-auth'
import { useQuery } from '@tanstack/react-query'
import { CACHE_TIMES, queryKeys, COMMON_QUERY_OPTIONS } from '@/lib/query-config'

export interface SuperAdminData {
  isSuperAdmin: boolean
  user: {
    id: string
    email: string
  } | null
}

export function useSuperAdmin() {
  const { user, isLoading: authLoading } = useAuth()

  const { data, isLoading: adminLoading, error } = useQuery({
    queryKey: queryKeys.superAdmin(user?.id || ''),
    queryFn: async (): Promise<SuperAdminData> => {
      if (!user) {
        console.log('useSuperAdmin: No user available')
        return { isSuperAdmin: false, user: null }
      }

      console.log('useSuperAdmin: Checking super admin status for user:', user.id)

      const response = await fetch('/api/super-admin-check', {
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
        }
      })

      if (!response.ok) {
        console.error('useSuperAdmin: API request failed:', response.status)
        throw new Error('Failed to check super admin status')
      }

      const result = await response.json()
      console.log('useSuperAdmin: API response:', { isSuperAdmin: result.isSuperAdmin })

      return result
    },
    enabled: !!user && !authLoading,
    staleTime: CACHE_TIMES.STATIC, // 30 minutes - super admin status rarely changes
    gcTime: CACHE_TIMES.STATIC * 2, // 60 minutes
    ...COMMON_QUERY_OPTIONS,
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      const errorStatus = (error as { status?: number })?.status
      if (errorStatus === 401 || errorStatus === 403) return false
      return failureCount < 2
    }
  })

  const result = {
    isSuperAdmin: data?.isSuperAdmin || false,
    isLoading: authLoading || adminLoading,
    user: data?.user || null,
    error
  }

  console.log('useSuperAdmin result:', {
    isSuperAdmin: result.isSuperAdmin,
    isLoading: result.isLoading,
    hasUser: !!result.user,
    error: result.error?.message
  })

  return result
}
