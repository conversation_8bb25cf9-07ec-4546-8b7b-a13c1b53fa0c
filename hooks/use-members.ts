import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useCompany } from "@/contexts/company-context";
import { CACHE_TIMES, COMMON_QUERY_OPTIONS, queryKeys } from "@/lib/query-config";

export interface Member {
  id: string;
  name: string;
  registration_date: string;
  loyalty_tier: string | null;
  loyalty_id: string;
  phone_number: string;
  email: string | null;
  available_points?: number;
  total_points?: number;
  initial_points?: number; // Add initial_points for member creation
  birthday?: string | null;
  birthday_month_day?: string | null;
  telegram_chat_id?: string | null;
  notes?: string | null;
  profile_image_url?: string | null;
}

/**
 * Hook to fetch all members
 */
export function useMembers(limit?: number) {
  const { company } = useCompany();
  const companyId = company?.id;

  return useQuery({
    queryKey: queryKeys.members(companyId || '', limit),
    queryFn: async () => {
      if (!companyId) {
        return { data: [] };
      }

      const url = limit
        ? `/api/members?companyId=${companyId}&limit=${limit}`
        : `/api/members?companyId=${companyId}`;

      const response = await fetch(url, {
        credentials: "include"
      });
      if (!response.ok) {
        throw new Error("Failed to fetch members");
      }
      return response.json();
    },
    enabled: !!companyId,
    staleTime: CACHE_TIMES.NORMAL,
    gcTime: CACHE_TIMES.NORMAL * 2,
    ...COMMON_QUERY_OPTIONS,
  });
}

/**
 * Hook to fetch a single member by ID
 */
export function useMember(memberId: string) {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: queryKeys.member(companyId || '', memberId),
    queryFn: async () => {
      if (!companyId || !memberId) {
        return null;
      }

      // Always fetch directly for individual member pages to ensure we have fresh data
      // This solves the profile image loading issue
      const response = await fetch(`/api/members/${memberId}?companyId=${companyId}`, {
        credentials: "include"
      });
      if (!response.ok) {
        throw new Error("Failed to fetch member");
      }

      // Get the fresh data from API
      const memberData = await response.json();

      // If we get successful data, update the member in the members query cache as well
      const membersData = queryClient.getQueryData<{ data: Member[] }>(queryKeys.members(companyId));
      if (membersData && membersData.data) {
        // Update the member in the members list cache
        queryClient.setQueryData(
          queryKeys.members(companyId),
          {
            data: membersData.data.map(m =>
              m.id === memberId ? { ...m, ...memberData } : m
            )
          }
        );
      }

      return memberData;
    },
    enabled: !!companyId && !!memberId,
    // Use shorter staleTime to ensure we check for fresh data more frequently
    staleTime: CACHE_TIMES.DYNAMIC,
    gcTime: CACHE_TIMES.NORMAL,
    ...COMMON_QUERY_OPTIONS,
  });
}

/**
 * Hook to find a member by ID from the already fetched members
 * This is useful when you already have the members data and don't want to make an additional request
 */
export function useFindMember(memberId: string | undefined) {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: ["findMember", companyId, memberId],
    queryFn: async () => {
      if (!companyId || !memberId) {
        return null;
      }

      // Try to use the existing members query cache
      const membersData = queryClient.getQueryData<{ data: Member[] }>(queryKeys.members(companyId));

      if (membersData) {
        return membersData.data.find(m => m.id === memberId) || null;
      }

      // If members aren't in cache yet, fetch all members
      const response = await fetch(`/api/members?companyId=${companyId}`, {
        credentials: "include"
      });
      if (!response.ok) {
        throw new Error("Failed to fetch members");
      }
      const data = await response.json();
      return data.data.find((m: Member) => m.id === memberId) || null;
    },
    enabled: !!companyId && !!memberId,
    staleTime: CACHE_TIMES.DYNAMIC,
    ...COMMON_QUERY_OPTIONS,
  });
}

/**
 * Hook to create a new member
 */
export function useCreateMember() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (memberData: Omit<Member, "id" | "registration_date">) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const response = await fetch("/api/members", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          ...memberData,
          company_id: companyId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Check for specific error types
        if (response.status === 409 || (errorData.details?.code === '23505' && errorData.details?.message?.includes('unique_phone_per_company'))) {
          throw new Error("A member with this phone number already exists");
        }

        throw new Error(errorData.error || "Failed to create member");
      }

      return response.json();
    },
    onSuccess: () => {
      // More specific invalidation using query keys factory
      if (companyId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.members(companyId) });
        queryClient.invalidateQueries({ queryKey: queryKeys.membersCount(companyId || '') });
      }
    },
  });
}

export interface MemberUpdateData {
  name: string;
  email?: string | null;
  phone_number: string;
  birthday?: string | null;
  birthday_month_day?: string | null;
  loyalty_tier?: string | null;
  notes?: string | null;
}

/**
 * Hook to update a member
 */
export function useUpdateMember() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ memberId, memberData }: { memberId: string; memberData: MemberUpdateData }) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const response = await fetch(`/api/members/${memberId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          ...memberData,
          company_id: companyId, // Add company_id to request body
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update member");
      }

      return response.json();
    },
    onSuccess: (_, variables) => {
      // More specific invalidation using query keys factory
      if (companyId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.members(companyId) });
        queryClient.invalidateQueries({ queryKey: queryKeys.member(companyId, variables.memberId) });
      }
    },
  });
}
