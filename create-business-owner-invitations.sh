#!/bin/bash

# Script to create business_owner_invitations table step by step
# This avoids timeout issues by breaking the SQL into smaller chunks

DB_URL="postgresql://postgres.vqltspteqqllvhyiupkf:<EMAIL>:6543/postgres"

echo "🚀 Creating business_owner_invitations table step by step..."

# Function to run SQL file with error checking
run_sql_step() {
    local step=$1
    local description=$2
    echo "📝 Step $step: $description"

    if psql "$DB_URL" -f "business-owner-invitations-step${step}.sql" 2>&1; then
        echo "✅ Step $step completed successfully"
    else
        echo "❌ Step $step failed"
        exit 1
    fi
    echo ""
}

# Run each step
run_sql_step 1 "Creating table structure"
run_sql_step 2 "Creating company_id index"
run_sql_step 3 "Creating token and email indexes"
run_sql_step 4 "Creating expires_at index"
run_sql_step 5 "Enabling Row Level Security"
run_sql_step 6 "Creating super admin policy"
run_sql_step 7 "Creating public read policy"
run_sql_step 8 "Granting permissions"

echo "🎉 All steps completed successfully!"
echo "✅ business_owner_invitations table is ready to use"
