-- Enhanced Business Owner Onboarding Schema Migration
-- Date: September 9, 2025
-- Description: Add comprehensive onboarding tracking to companies table

-- Add new columns for enhanced onboarding tracking
ALTER TABLE companies ADD COLUMN IF NOT EXISTS onboarding_step_completed JSONB DEFAULT '{}';
ALTER TABLE companies ADD COLUMN IF NOT EXISTS onboarding_skipped_steps JSONB DEFAULT '[]';
ALTER TABLE companies ADD COLUMN IF NOT EXISTS onboarding_completion_date TIMESTAMPTZ;

-- Add comments for documentation
COMMENT ON COLUMN companies.onboarding_step_completed IS 'JSON object tracking which onboarding steps have been completed, e.g., {"branding": true, "tiers": true, "rewards": false}';
COMMENT ON COLUMN companies.onboarding_skipped_steps IS 'JSON array of step IDs that were skipped, e.g., ["staff_invites", "advanced_rewards"]';
COMMENT ON COLUMN companies.onboarding_completion_date IS 'Timestamp when the onboarding was marked as fully complete (100%)';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_companies_onboarding_step_completed ON companies USING gin (onboarding_step_completed);
CREATE INDEX IF NOT EXISTS idx_companies_onboarding_completion_date ON companies (onboarding_completion_date);

-- Create function to calculate onboarding progress with enhanced metrics
CREATE OR REPLACE FUNCTION get_enhanced_onboarding_status(p_user_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  company_record record;
  result jsonb;
  essential_progress integer := 0;
  business_progress integer := 0;
  advanced_progress integer := 0;
  overall_progress integer := 0;
BEGIN
  -- Get company and metrics
  SELECT
    c.id,
    c.name,
    c.created_at,
    c.onboarding_step_completed,
    c.onboarding_skipped_steps,
    c.onboarding_completion_date,
    c.logo_url,
    c.primary_color,
    COALESCE(counts.member_count, 0) as member_count,
    COALESCE(counts.transaction_count, 0) as transaction_count,
    COALESCE(counts.reward_count, 0) as reward_count,
    COALESCE(counts.tier_count, 0) as tier_count,
    COALESCE(counts.cashier_count, 0) as cashier_count
  INTO company_record
  FROM public.companies c
  LEFT JOIN (
    SELECT
      c.id as company_id,
      COUNT(DISTINCT lm.id) as member_count,
      COUNT(DISTINCT pt.id) as transaction_count,
      COUNT(DISTINCT r.id) as reward_count,
      COUNT(DISTINCT td.id) as tier_count,
      COUNT(DISTINCT ci.id) as cashier_count
    FROM public.companies c
    LEFT JOIN public.loyalty_members lm ON c.id = lm.company_id
    LEFT JOIN public.points_transactions pt ON c.id = pt.company_id
    LEFT JOIN public.rewards r ON c.id = r.company_id
    LEFT JOIN public.tier_definitions td ON c.id = td.company_id
    LEFT JOIN public.cashier_invitations ci ON c.id = ci.company_id AND ci.used_at IS NOT NULL
    WHERE c.administrator_id = p_user_id
    GROUP BY c.id
  ) counts ON c.id = counts.company_id
  WHERE c.administrator_id = p_user_id
  ORDER BY c.created_at DESC
  LIMIT 1;

  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'hasCompany', false,
      'completionPercentage', 0,
      'nextSteps', jsonb_build_array('Create your company profile')
    );
  END IF;

  -- Calculate essential progress (40% of total)
  -- Company exists: +10%
  essential_progress := essential_progress + 10;

  -- Has branding (logo or custom color): +15%
  IF company_record.logo_url IS NOT NULL OR company_record.primary_color != '#000000' THEN
    essential_progress := essential_progress + 15;
  END IF;

  -- Has basic tiers: +15%
  IF company_record.tier_count >= 1 THEN
    essential_progress := essential_progress + 15;
  END IF;

  -- Calculate business progress (40% of total)
  -- Has at least one reward: +20%
  IF company_record.reward_count >= 1 THEN
    business_progress := business_progress + 20;
  END IF;

  -- Has at least one member: +20%
  IF company_record.member_count >= 1 THEN
    business_progress := business_progress + 20;
  END IF;

  -- Calculate advanced progress (20% of total)
  -- Has multiple tiers: +7%
  IF company_record.tier_count >= 2 THEN
    advanced_progress := advanced_progress + 7;
  END IF;

  -- Has multiple rewards: +6%
  IF company_record.reward_count >= 3 THEN
    advanced_progress := advanced_progress + 6;
  END IF;

  -- Has active member base: +4%
  IF company_record.member_count >= 5 THEN
    advanced_progress := advanced_progress + 4;
  END IF;

  -- Has staff/cashiers: +3%
  IF company_record.cashier_count >= 1 THEN
    advanced_progress := advanced_progress + 3;
  END IF;

  overall_progress := essential_progress + business_progress + advanced_progress;

  result := jsonb_build_object(
    'hasCompany', true,
    'company', jsonb_build_object(
      'id', company_record.id,
      'name', company_record.name,
      'memberCount', company_record.member_count,
      'transactionCount', company_record.transaction_count,
      'rewardCount', company_record.reward_count,
      'tierCount', company_record.tier_count,
      'cashierCount', company_record.cashier_count,
      'hasLogo', company_record.logo_url IS NOT NULL,
      'hasCustomColor', company_record.primary_color != '#000000'
    ),
    'progress', jsonb_build_object(
      'essential', essential_progress,
      'business', business_progress,
      'advanced', advanced_progress,
      'overall', overall_progress
    ),
    'stepCompleted', COALESCE(company_record.onboarding_step_completed, '{}'::jsonb),
    'skippedSteps', COALESCE(company_record.onboarding_skipped_steps, '[]'::jsonb),
    'completionDate', company_record.onboarding_completion_date,
    'isOnboardingComplete', overall_progress >= 60, -- 60% minimum for "complete"
    'completionPercentage', overall_progress,
    'nextSteps', CASE
      WHEN essential_progress < 40 THEN
        jsonb_build_array(
          CASE WHEN company_record.logo_url IS NULL AND company_record.primary_color = '#000000' THEN 'Add company branding (logo or colors)' END,
          CASE WHEN company_record.tier_count = 0 THEN 'Set up loyalty tiers' END
        )
      WHEN business_progress < 40 THEN
        jsonb_build_array(
          CASE WHEN company_record.reward_count = 0 THEN 'Create your first reward' END,
          CASE WHEN company_record.member_count = 0 THEN 'Add your first loyalty member' END
        )
      ELSE
        jsonb_build_array('Consider inviting staff members', 'Create additional rewards')
    END
  );

  RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_enhanced_onboarding_status(uuid) TO authenticated;

-- Migrate existing data
-- Set completion date for companies that are already marked as complete
UPDATE companies
SET onboarding_completion_date = created_at + INTERVAL '1 day'
WHERE onboarding_completed = true AND onboarding_completion_date IS NULL;

-- Initialize step completion tracking for existing companies
UPDATE companies
SET onboarding_step_completed = '{}'::jsonb
WHERE onboarding_step_completed IS NULL;

-- Initialize skipped steps array for existing companies
UPDATE companies
SET onboarding_skipped_steps = '[]'::jsonb
WHERE onboarding_skipped_steps IS NULL;
