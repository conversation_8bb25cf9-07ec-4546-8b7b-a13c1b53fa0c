import { getServiceRoleClient } from '@/lib/supabase'

export class PremiumBotCreationService {
  private supabase = getServiceRoleClient()

  async setupPremiumBotWithToken(companyId: string, businessName: string, botToken: string) {
    try {
      console.log(`[PremiumBot] Setting up bot for ${businessName} with token ${botToken.substring(0, 10)}...`)

      // 1. Validate bot token and get bot info
      const botInfo = await this.validateAndGetBotInfo(botToken)
      console.log(`[PremiumBot] Bot validated: @${botInfo.username}`)

      // 2. Generate webhook URL and secret
      const encodedBotToken = encodeURIComponent(botToken)
      const webhookUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/telegram/premium/${encodedBotToken}`
      const webhookSecret = await this.generateWebhookSecret()

      // 3. Create bot configuration in database
      const { data: botConfig, error } = await this.supabase
        .from('bot_configurations')
        .insert({
          company_id: companyId,
          bot_token: botToken,
          bot_username: botInfo.username,
          bot_name: botInfo.first_name,
          bot_description: `Official loyalty program bot for ${businessName}. Earn points, redeem rewards, and get exclusive offers!`,
          webhook_url: webhookUrl,
          webhook_secret: webhookSecret,
          creation_status: 'configured',
          branding_config: {
            businessName,
            welcomeMessage: `Welcome to ${businessName}! 🎉`,
            brandColor: '#6366f1',
            supportContact: `Contact ${businessName} for support`
          }
        })
        .select()
        .single()

      if (error) {
        console.error('[PremiumBot] Database error:', error)
        throw new Error(`Database error: ${error.message}`)
      }

      console.log(`[PremiumBot] Bot configuration created with ID: ${botConfig.id}`)

      // 4. Set up webhook with Telegram
      await this.setupBotWebhook(botToken, webhookUrl, webhookSecret)
      console.log(`[PremiumBot] Webhook configured: ${webhookUrl}`)

      // 5. Configure bot commands
      await this.setupBotCommands(botToken, businessName)
      console.log(`[PremiumBot] Bot commands configured`)

      // 6. Update bot status to active
      await this.supabase
        .from('bot_configurations')
        .update({
          creation_status: 'active',
          activated_at: new Date().toISOString()
        })
        .eq('id', botConfig.id)

      // 7. Update company record
      await this.supabase
        .from('companies')
        .update({
          bot_configuration_id: botConfig.id,
          bot_tier: 'premium'
        })
        .eq('id', companyId)

      console.log(`[PremiumBot] Setup completed successfully for ${businessName}`)

      return {
        success: true,
        bot_username: botInfo.username,
        bot_name: botInfo.first_name,
        webhook_url: webhookUrl,
        share_url: `https://t.me/${botInfo.username}`,
        config_id: botConfig.id
      }

    } catch (error) {
      console.error('[PremiumBot] Setup failed:', error)

      // Mark as failed in database if config was created
      try {
        await this.supabase
          .from('bot_configurations')
          .update({ creation_status: 'failed' })
          .eq('company_id', companyId)
      } catch (dbError) {
        console.error('[PremiumBot] Failed to update error status:', dbError)
      }

      throw new Error(`Failed to setup premium bot: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async validateAndGetBotInfo(token: string) {
    try {
      const response = await fetch(`https://api.telegram.org/bot${token}/getMe`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()

      if (!result.ok) {
        throw new Error(`Telegram API error: ${result.description}`)
      }

      const botInfo = result.result

      // Validate bot info
      if (!botInfo.username) {
        throw new Error('Bot must have a username')
      }

      if (!botInfo.username.toLowerCase().endsWith('bot')) {
        throw new Error('Bot username must end with "bot"')
      }

      return botInfo

    } catch (error) {
      console.error('[PremiumBot] Bot validation failed:', error)
      throw new Error(`Invalid bot token: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async setupBotWebhook(token: string, webhookUrl: string, secret: string) {
    try {
      // Skip webhook setup in development if using HTTP localhost
      if (webhookUrl.startsWith('http://localhost') || webhookUrl.startsWith('http://127.0.0.1')) {
        console.log('[PremiumBot] Skipping webhook setup in local development (HTTP not allowed by Telegram)')
        console.log('[PremiumBot] Webhook URL would be:', webhookUrl)
        console.log('[PremiumBot] For local testing, use ngrok or similar HTTPS tunnel')
        return
      }

      const response = await fetch(`https://api.telegram.org/bot${token}/setWebhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url: webhookUrl,
          secret_token: secret,
          allowed_updates: ['message', 'callback_query'],
          drop_pending_updates: true,
          max_connections: 40
        })
      })

      const result = await response.json()

      if (!result.ok) {
        throw new Error(`Webhook setup failed: ${result.description}`)
      }

      console.log('[PremiumBot] Webhook set successfully')

    } catch (error) {
      console.error('[PremiumBot] Webhook setup error:', error)
      throw new Error(`Failed to set webhook: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async setupBotCommands(token: string, businessName: string) {
    try {
      const commands = [
        { command: 'start', description: `Join ${businessName} loyalty program` },
        { command: 'balance', description: 'Check your points balance' },
        { command: 'rewards', description: 'Browse available rewards' },
        { command: 'tier', description: 'View your loyalty tier status' },
        { command: 'history', description: 'View transaction history' },
        { command: 'profile', description: 'View your profile information' },
        { command: 'help', description: 'Get help and support' }
      ]

      const response = await fetch(`https://api.telegram.org/bot${token}/setMyCommands`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ commands })
      })

      const result = await response.json()

      if (!result.ok) {
        throw new Error(`Commands setup failed: ${result.description}`)
      }

      console.log('[PremiumBot] Commands set successfully')

    } catch (error) {
      console.error('[PremiumBot] Commands setup error:', error)
      throw new Error(`Failed to set commands: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async generateWebhookSecret(): Promise<string> {
    const crypto = await import('crypto')
    return crypto.randomBytes(32).toString('hex')
  }

  async getBotConfiguration(companyId: string) {
    const { data, error } = await this.supabase
      .from('bot_configurations')
      .select('*, companies(*)')
      .eq('company_id', companyId)
      .single()

    if (error) {
      console.error('[PremiumBot] Failed to get bot configuration:', error)
      return null
    }

    return data
  }

  async getBotConfigurationByToken(botToken: string) {
    const { data, error } = await this.supabase
      .from('bot_configurations')
      .select('*, companies(*)')
      .eq('bot_token', botToken)
      .eq('is_active', true)
      .single()

    if (error) {
      console.error('[PremiumBot] Failed to get bot configuration by token:', error)
      return null
    }

    return data
  }

  async updateBotHealthCheck(configId: string) {
    await this.supabase
      .from('bot_configurations')
      .update({
        last_health_check: new Date().toISOString()
      })
      .eq('id', configId)
  }

  async deactivateBot(companyId: string) {
    try {
      const config = await this.getBotConfiguration(companyId)

      if (!config) {
        throw new Error('Bot configuration not found')
      }

      // Delete webhook
      await fetch(`https://api.telegram.org/bot${config.bot_token}/deleteWebhook`, {
        method: 'POST'
      })

      // Deactivate in database
      await this.supabase
        .from('bot_configurations')
        .update({
          is_active: false,
          creation_status: 'deactivated'
        })
        .eq('id', config.id)

      // Update company
      await this.supabase
        .from('companies')
        .update({
          bot_tier: 'standard',
          bot_configuration_id: null
        })
        .eq('id', companyId)

      console.log(`[PremiumBot] Bot deactivated for company ${companyId}`)

    } catch (error) {
      console.error('[PremiumBot] Deactivation failed:', error)
      throw error
    }
  }
}
